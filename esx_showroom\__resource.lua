resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'

client_scripts {
	'@Framework/locale/locale.lua',
	'locales/fr.lua',
	'config.lua',
	'@epicenter/vendors/RageUI/RMenu.lua',
	'@epicenter/vendors/RageUI/menu/RageUI.lua',
	'@epicenter/vendors/RageUI/menu/Menu.lua',
	'@epicenter/vendors/RageUI/menu/MenuController.lua',
	'@epicenter/vendors/RageUI/components/Audio.lua',
	'@epicenter/vendors/RageUI/components/Enum.lua',
	'@epicenter/vendors/RageUI/components/Keys.lua',
	'@epicenter/vendors/RageUI/components/Rectangle.lua',
	'@epicenter/vendors/RageUI/components/Sprite.lua',
	'@epicenter/vendors/RageUI/components/Text.lua',
	'@epicenter/vendors/RageUI/components/Visual.lua',
	'@epicenter/vendors/RageUI/menu/elements/ItemsBadge.lua',
	'@epicenter/vendors/RageUI/menu/elements/ItemsColour.lua',
	'@epicenter/vendors/RageUI/menu/elements/PanelColour.lua',
	'@epicenter/vendors/RageUI/menu/items/UIButton.lua',
	'@epicenter/vendors/RageUI/menu/items/UICheckBox.lua',
	'@epicenter/vendors/RageUI/menu/items/UIInfo.lua',
	'@epicenter/vendors/RageUI/menu/items/UILine.lua',
	'@epicenter/vendors/RageUI/menu/items/UIList.lua',
	'@epicenter/vendors/RageUI/menu/items/UISeparator.lua',
	'@epicenter/vendors/RageUI/menu/items/UISlider.lua',
	'@epicenter/vendors/RageUI/menu/items/UISliderHeritage.lua',
	'@epicenter/vendors/RageUI/menu/items/UISliderProgress.lua',
	'@epicenter/vendors/RageUI/menu/panels/UIColourPanel.lua',
	'@epicenter/vendors/RageUI/menu/panels/UIGridPanel.lua',
	'@epicenter/vendors/RageUI/menu/panels/UIPercentagePanel.lua',
	'@epicenter/vendors/RageUI/menu/panels/UISpritPanel.lua',
	'@epicenter/vendors/RageUI/menu/panels/UIStatisticsPanel.lua',
	'@epicenter/vendors/RageUI/menu/windows/UIHeritage.lua',
	'client/main.lua'
}

server_scripts {
	'@oxmysql/lib/MySQL.lua',
	'@Framework/locale/locale.lua',
	'locales/fr.lua',
	'config.lua',
	'server/main.lua'
}

dependency 'Framework'