<svg width="375" height="110" viewBox="0 0 375 110" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="375" height="110" rx="55" fill="black"/>
<circle cx="320" cy="55" r="29" fill="#090609"/>
<circle cx="320" cy="55.5" r="16" fill="#131423"/>
<g clip-path="url(#clip0_204_21)">
<rect x="307" y="42.5" width="26" height="26" rx="13" fill="#07144C"/>
<g filter="url(#filter0_f_204_21)">
<path d="M311 45H329C329 45 330 51.5 330 56.5C330 61.5 329 68 329 68H311C311 68 310 62 310 56.5C310 51 311 45 311 45Z" fill="#030303" fill-opacity="0.8"/>
</g>
<g filter="url(#filter1_f_204_21)">
<ellipse cx="320" cy="61.5" rx="5" ry="4.5" fill="url(#paint0_linear_204_21)"/>
</g>
<g filter="url(#filter2_f_204_21)">
<circle cx="320" cy="63" r="3" fill="url(#paint1_radial_204_21)" fill-opacity="0.8"/>
</g>
<circle cx="320" cy="55.5" r="8" fill="url(#paint2_radial_204_21)"/>
<g filter="url(#filter3_f_204_21)">
<circle cx="320" cy="55.5" r="10" fill="url(#paint3_radial_204_21)"/>
</g>
</g>
<rect x="307.5" y="43" width="25" height="25" rx="12.5" stroke="url(#paint4_linear_204_21)"/>
<defs>
<filter id="filter0_f_204_21" x="307" y="42" width="26" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.5" result="effect1_foregroundBlur_204_21"/>
</filter>
<filter id="filter1_f_204_21" x="310" y="52" width="20" height="19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.5" result="effect1_foregroundBlur_204_21"/>
</filter>
<filter id="filter2_f_204_21" x="316" y="59" width="8" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_204_21"/>
</filter>
<filter id="filter3_f_204_21" x="307" y="42.5" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.5" result="effect1_foregroundBlur_204_21"/>
</filter>
<linearGradient id="paint0_linear_204_21" x1="320" y1="57" x2="320" y2="66" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D869C" stop-opacity="0.67"/>
<stop offset="0.371348" stop-color="#2371C6" stop-opacity="0.699708"/>
<stop offset="1" stop-color="#040AAD" stop-opacity="0.75"/>
</linearGradient>
<radialGradient id="paint1_radial_204_21" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(320 60) rotate(90) scale(6)">
<stop stop-color="#005267"/>
<stop offset="0.595648" stop-color="#415967"/>
<stop offset="1" stop-color="#8700C7"/>
</radialGradient>
<radialGradient id="paint2_radial_204_21" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(320 47.1) rotate(90) scale(9.9)">
<stop stop-color="#5978DD"/>
<stop offset="0.510478" stop-color="#312FAD" stop-opacity="0.61"/>
<stop offset="1" stop-color="#1C1C86" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_204_21" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(320 45) rotate(90) scale(11)">
<stop stop-color="white" stop-opacity="0.38"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint4_linear_204_21" x1="320" y1="42.5" x2="320" y2="68.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.06"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_204_21">
<rect x="307" y="42.5" width="26" height="26" rx="13" fill="white"/>
</clipPath>
</defs>
</svg>
