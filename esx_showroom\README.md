# ESX Showroom - Version Adaptée

## Description
Script de showroom pour FiveM adapté pour fonctionner avec un framework personnalisé basé sur ESX.

## Modifications apportées

### 1. Dépendances
- Remplacé `es_extended` par `Framework` (votre framework personnalisé)
- Remplacé `mysql-async` par `oxmysql`
- Mis à jour les chemins vers les fichiers de locale

### 2. Interface utilisateur
- Remplacé `ESX.UI.Menu` par `RageUI` pour la compatibilité avec votre framework
- Adapté le système de menu pour utiliser RageUI

### 3. Notifications
- Remplacé `ESX.ShowAdvancedNotification` par `exports.notify:MontreToiBasique`
- Utilise votre système de notification personnalisé

### 4. Base de données
- Adapté les requêtes SQL pour utiliser `oxmysql` au lieu de `mysql-async`

## Installation

1. Placez le dossier `esx_showroom` dans votre dossier `resources/`
2. Ajoutez `ensure esx_showroom` dans votre `server.cfg`
3. Assurez-vous que les dépendances suivantes sont démarrées :
   - `Framework`
   - `oxmysql`
   - `notify`

## Configuration

Le script utilise la même configuration que l'original. Modifiez le fichier `config.lua` selon vos besoins.

## Utilisation

1. Assurez-vous d'avoir le job `cardealer`
2. Rendez-vous aux emplacements configurés dans le showroom
3. Utilisez la touche E pour interagir avec les zones
4. Sélectionnez un véhicule dans le menu RageUI
5. Utilisez la zone de suppression pour ranger tous les véhicules

## Compatibilité

Ce script est maintenant compatible avec :
- Votre framework personnalisé basé sur ESX
- oxmysql
- RageUI
- Votre système de notification personnalisé

## Support

Pour toute question ou problème, consultez la documentation de votre framework ou contactez votre équipe de développement.

## Requirements
- https://github.com/esx-framework/es_extended
- https://github.com/brouznouf/fivem-mysql-async
- https://github.com/esx-framework/esx_vehicleshop

## Usage
- You can spawn every vehicle in the ``vehicles`` table
- Vehicles can't be stolen

## YMAP
https://fr.gta5-mods.com/maps/car-dealer-update-ymap

## Screenshots
[![](https://i.imgur.com/BJAlksg.png)]()
[![](https://i.imgur.com/2NN5lHl.png)]()