ESX	= nil
local PlayerData = {}
local CurrentAction = nil
local CurrentActionData = {}
local HasAlreadyEnteredMarker = false
local LastZone = nil
local ShowroomMenu = nil
local isCardealer = false
local showroomMenuOpen = false

Citizen.CreateThread(function()
	while ESX == nil do
		TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
		Citizen.Wait(0)
	end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	PlayerData = ESX.GetPlayerData()
	isCardealer = (PlayerData.job ~= nil and PlayerData.job.name == 'cardealer')
end)

AddEventHandler('esx_showroom:hasEnteredMarker', function(zone)
	CurrentAction     = 'get_vehicle_out'
	CurrentActionMsg  = _U('get_vehicle_out')
	CurrentActionData = {zone = zone}
end)

AddEventHandler('esx_showroom:hasExitedMarker', function(zone)
	CurrentAction = nil
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
	isCardealer = (job ~= nil and job.name == 'cardealer')
end)

-- Thread optimisé pour les markers
Citizen.CreateThread(function()
	while true do
		if isCardealer then
			local coords = GetEntityCoords(GetPlayerPed(-1))
			local playerCoords = vector3(coords.x, coords.y, coords.z)
			local drawDistance = Config.DrawDistance
			
			-- Dessiner les markers des zones
			for k,v in pairs(Config.Zones) do
				for i = 1, #v.Pos, 1 do
					local zoneCoords = vector3(v.Pos[i].x, v.Pos[i].y, v.Pos[i].z)
					local distance = #(playerCoords - zoneCoords)
					
					if distance < drawDistance then
						DrawMarker(
							Config.Type, 
							v.Pos[i].x, v.Pos[i].y, v.Pos[i].z, 
							0.0, 0.0, 0.0, 
							0, 0.0, 0.0, 
							Config.Size.x, Config.Size.y, Config.Size.z, 
							Config.Color.r, Config.Color.g, Config.Color.b, 150, 
							false, true, 2, true, false, false, false
						)
					end
				end
			end
			
			-- Dessiner le marker de suppression
			local deleterCoords = vector3(Config.VehicleDeleter[1].x, Config.VehicleDeleter[1].y, Config.VehicleDeleter[1].z)
			local deleterDistance = #(playerCoords - deleterCoords)
			
			if deleterDistance < drawDistance then
				DrawMarker(
					Config.Type, 
					Config.VehicleDeleter[1].x, Config.VehicleDeleter[1].y, Config.VehicleDeleter[1].z, 
					0.0, 0.0, 0.0, 
					0, 0.0, 0.0, 
					Config.Size.x, Config.Size.y, Config.Size.z, 
					200, 0, 0, 150, 
					false, true, 2, true, false, false, false
				)
				
				if deleterDistance <= 1.5 then
					DrawTxt(_U('delete_vehicles'))
					if IsControlJustPressed(1, 51) then 
						DeleteShowroomVehicles()
					end
				end
			end
			
			Citizen.Wait(0)
		else
			Citizen.Wait(1000) -- Attendre plus longtemps si pas cardealer
		end
	end
end)

-- Thread optimisé pour la détection des zones
Citizen.CreateThread(function()
	while true do
		if isCardealer then
			local coords = GetEntityCoords(GetPlayerPed(-1))
			local playerCoords = vector3(coords.x, coords.y, coords.z)
			local isInMarker = false
			local currentZone = nil

			for k,v in pairs(Config.Zones) do
				for i = 1, #v.Pos, 1 do
					local zoneCoords = vector3(v.Pos[i].x, v.Pos[i].y, v.Pos[i].z)
					local distance = #(playerCoords - zoneCoords)
					
					if distance < Config.Size.x then
						isInMarker = true
						currentZone = k
						LastZone = k
						break
					end
				end
				if isInMarker then break end
			end
			
			if isInMarker and not HasAlreadyEnteredMarker then
				HasAlreadyEnteredMarker = true
				TriggerEvent('esx_showroom:hasEnteredMarker', currentZone)
			elseif not isInMarker and HasAlreadyEnteredMarker then
				HasAlreadyEnteredMarker = false
				TriggerEvent('esx_showroom:hasExitedMarker', LastZone)
			end
			
			Citizen.Wait(100)
		else
			Citizen.Wait(1000)
		end
	end
end)

-- Thread pour les actions
Citizen.CreateThread(function()
	while true do
		if CurrentAction ~= nil then
			SetTextComponentFormat('STRING')
			AddTextComponentString(CurrentActionMsg)
			DisplayHelpTextFromStringLabel(0, 0, 1, -1)

			if IsControlJustReleased(0, 38) or (IsControlJustReleased(0, 175) and not IsInputDisabled(0)) then
				if CurrentAction == 'get_vehicle_out' then
					PutVehicleInShowroom()		
				end
				CurrentAction = nil	
			end
			Citizen.Wait(0)
		else
			Citizen.Wait(500)
		end
	end
end)

function PutVehicleInShowroom()
	local vehicleModel = nil
	for k,v in pairs(Config.Zones) do
		for i = 1, #v.Pos, 1 do
			if(LastZone == k) then
				if v.Pos[i].isAvailable then
					if not DoesEntityExist(GetClosestVehicle(v.Pos[i].x + 2.5, v.Pos[i].y - 0.2, v.Pos[i].z, 1.5, 0, 70)) then
							ESX.TriggerServerCallback('esx_showroom:getCars', function(cars)
								OpenShowroomMenu(cars, v, i)
							end)
					else
						exports.notify:MontreToiBasique(_U('vehicle_blocking'), nil, nil, true, nil, "Showroom", "Erreur", "error")
					end
				else
					local vehicleToDelete = GetClosestVehicle(v.Pos[i].x + 2.5, v.Pos[i].y - 0.2, v.Pos[i].z, 1.5, 0, 70)
					if DoesEntityExist(vehicleToDelete) then
						DeleteVehicle(vehicleToDelete)
						v.Pos[i].isAvailable = true
					end			
				end
			end
		end
	end
end

function OpenShowroomMenu(cars, zoneData, zoneIndex)
	-- Créer le menu
	ShowroomMenu = RageUI.CreateMenu("Showroom", "Véhicules d'exposition")
	showroomMenuOpen = true
	
	-- Thread pour gérer le menu
	Citizen.CreateThread(function()
		while showroomMenuOpen do
			Citizen.Wait(1)
			
			RageUI.IsVisible(ShowroomMenu, function()
				-- Ajouter les boutons pour chaque véhicule
				for i=1, #cars, 1 do
					RageUI.Button(cars[i].name, nil, {}, true, {
						onSelected = function()
							-- Créer le véhicule
							local vehicleModel = cars[i].model
							RequestModel(vehicleModel)
							while not HasModelLoaded(vehicleModel) do
								Citizen.Wait(10)
							end

							local vehicle = CreateVehicle(vehicleModel, zoneData.Pos[zoneIndex].x + 2.5, zoneData.Pos[zoneIndex].y - 0.2, zoneData.Pos[zoneIndex].z, zoneData.Pos[zoneIndex].h, true, true)
							local id = NetworkGetNetworkIdFromEntity(vehicle)
							SetVehicleNumberPlateText(vehicle, 'Araziz')
							SetEntityAsMissionEntity(vehicle, true)
							SetVehicleOnGroundProperly(vehicle)
							SetVehicleHasBeenOwnedByPlayer(vehicle, true)
							SetNetworkIdCanMigrate(id, true)
							SetVehRadioStation(vehicle, "OFF")
							SetVehicleDirtLevel(vehicle, 0.0)
							FreezeEntityPosition(vehicle, true)
							zoneData.Pos[zoneIndex].isAvailable = false
							
							-- Fermer le menu et notifier
							showroomMenuOpen = false
							RageUI.CloseAll()
							ShowroomMenu = nil
							exports.notify:MontreToiBasique("Véhicule placé avec succès", nil, nil, true, nil, "Showroom", "Succès", "success")
						end
					})
				end
			end)
			
			if not RageUI.Visible(ShowroomMenu) then
				showroomMenuOpen = false
				ShowroomMenu = nil
				break
			end
		end
	end)
	
	-- Ouvrir le menu après avoir créé le thread
	RageUI.Visible(ShowroomMenu, true)
end

function DeleteShowroomVehicles()
	for k,v in pairs(Config.Zones) do
		for i = 1, #v.Pos, 1 do
			local vehicleToDelete = GetClosestVehicle(v.Pos[i].x + 2.5, v.Pos[i].y - 0.2, v.Pos[i].z, 1.5, 0, 70)
			if DoesEntityExist(vehicleToDelete) then
				DeleteVehicle(vehicleToDelete)
				v.Pos[i].isAvailable = true	
			end	
		end
	end
	exports.notify:MontreToiBasique(_U('vehicles_deleted'), nil, nil, true, nil, "Showroom", "Succès", "success")
end

function DrawTxt(text)
	SetTextComponentFormat('STRING')
	AddTextComponentString(text)
	DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end