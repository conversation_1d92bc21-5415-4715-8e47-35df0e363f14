Config.Vehicles = {
    --- Compacts (0)
    asbo = { model = 'asbo',name = 'Asbo', brand = 'Maxwell',category = 'compacts', type = 'automobile',},
    blista = { model = 'blista',        name = 'Blista',                        brand = 'Dinka',             category = 'compacts',       type = 'automobile' },
    brioso = { model = 'brioso',        name = 'Brioso R/A',                    brand = '<PERSON><PERSON><PERSON>',            category = 'compacts',       type = 'automobile' },
    club = { model = 'club',          name = 'Club',                          brand = 'BF',                 category = 'compacts',       type = 'automobile' },
    dilettante = { model = 'dilettante',    name = 'Dilettante',                    brand = 'Karin',              category = 'compacts',       type = 'automobile' },
    dilettante2 = { model = 'dilettante2',   name = 'Dilettante Patrol',             brand = 'Karin',             category = 'compacts',       type = 'automobile' },
    kanjo = { model = 'kanjo',         name = 'Blista Kanjo',                  brand = 'Dinka',             category = 'compacts',       type = 'automobile' },
    issi2 = { model = 'issi2',         name = 'Issi',                          brand = 'Weeny',              category = 'compacts',       type = 'automobile' },
    issi3 = { model = 'issi3',         name = 'Issi Classic',                  brand = 'Weeny',              category = 'compacts',       type = 'automobile' },
    issi4 = { model = 'issi4',         name = 'Issi Arena',                    brand = 'Weeny',             category = 'compacts',       type = 'automobile' },
    issi5 = { model = 'issi5',         name = 'Issi Future Shock',             brand = 'Weeny',             category = 'compacts',       type = 'automobile' },
    issi6 = { model = 'issi6',         name = 'Issi Nightmare',                brand = 'Weeny',             category = 'compacts',       type = 'automobile' },
    panto = { model = 'panto',         name = 'Panto',                         brand = 'Benefactor',         category = 'compacts',       type = 'automobile' },
    prairie = { model = 'prairie',       name = 'Prairie',                       brand = 'Bollokan',          category = 'compacts',       type = 'automobile' },
    rhapsody = { model = 'rhapsody',      name = 'Rhapsody',                      brand = 'Declasse',          category = 'compacts',       type = 'automobile' },
    brioso2 = { model = 'brioso2',       name = 'Brioso 300',                    brand = 'Grotti',            category = 'compacts',       type = 'automobile' },
    weevil = { model = 'weevil',        name = 'Weevil',                        brand = 'BF',                 category = 'compacts',       type = 'automobile' },
    issi7 = { model = 'issi7',         name = 'Issi Sport',                    brand = 'Weeny',            category = 'compacts',       type = 'automobile' },
    blista2 = { model = 'blista2',       name = 'Blista Compact',                brand = 'Dinka',             category = 'compacts',       type = 'automobile' },
    blista3 = { model = 'blista3',       name = 'Blista Go Go Monkey',           brand = 'Dinka',             category = 'compacts',       type = 'automobile' },
    brioso3 = { model = 'brioso3',       name = 'Brioso 300 Widebody',           brand = 'Grotti',           category = 'compacts',       type = 'automobile' },
    boor = { model = 'boor',          name = 'Boor',                          brand = 'Karin',             category = 'compacts',       type = 'automobile' },
    --- Sedans (1)
    asea = { model = 'asea',          name = 'Asea',                          brand = 'Declasse',           category = 'sedans',         type = 'automobile' },
    asterope = { model = 'asterope',      name = 'Asterope',                      brand = 'Karin',             category = 'sedans',         type = 'automobile' },
    cog55 = { model = 'cog55',         name = 'Cognoscenti 55',                brand = 'Enus',              category = 'sedans',         type = 'automobile' },
    cognoscenti = { model = 'cognoscenti',   name = 'Cognoscenti',                   brand = 'Enus',              category = 'sedans',         type = 'automobile' },
    emperor = { model = 'emperor',       name = 'Emperor',                       brand = 'Albany',             category = 'sedans',         type = 'automobile' },
    fugitive = { model = 'fugitive',      name = 'Fugitive',                      brand = 'Cheval',            category = 'sedans',         type = 'automobile' },
    glendale = { model = 'glendale',      name = 'Glendale',                      brand = 'Benefactor',         category = 'sedans',         type = 'automobile' },
    glendale2 = { model = 'glendale2',     name = 'Glendale Custom',               brand = 'Benefactor',        category = 'sedans',         type = 'automobile' },
    ingot = { model = 'ingot',         name = 'Ingot',                         brand = 'Vulcar',             category = 'sedans',         type = 'automobile' },
    intruder = { model = 'intruder',      name = 'Intruder',                      brand = 'Karin',             category = 'sedans',         type = 'automobile' },
    premier = { model = 'premier',       name = 'Premier',                       brand = 'Declasse',          category = 'sedans',         type = 'automobile' },
    primo = { model = 'primo',         name = 'Primo',                         brand = 'Albany',             category = 'sedans',         type = 'automobile' },
    primo2 = { model = 'primo2',        name = 'Primo Custom',                  brand = 'Albany',            category = 'sedans',         type = 'automobile' },
    regina = { model = 'regina',        name = 'Regina',                        brand = 'Dundreary',          category = 'sedans',         type = 'automobile' },
    stafford = { model = 'stafford',      name = 'Stafford',                      brand = 'Enus',              category = 'sedans',         type = 'automobile' },
    stanier = { model = 'stanier',       name = 'Stanier',                       brand = 'Vapid',             category = 'sedans',         type = 'automobile' },
    stratum = { model = 'stratum',       name = 'Stratum',                       brand = 'Zirconium',         category = 'sedans',         type = 'automobile' },
    stretch = { model = 'stretch',       name = 'Stretch',                       brand = 'Dundreary',         category = 'sedans',         type = 'automobile' },
    superd = { model = 'superd',        name = 'Super Diamond',                 brand = 'Enus',              category = 'sedans',         type = 'automobile' },
    surge = { model = 'surge',         name = 'Surge',                         brand = 'Cheval',            category = 'sedans',         type = 'automobile' },
    tailgater = { model = 'tailgater',     name = 'Tailgater',                     brand = 'Obey',              category = 'sedans',         type = 'automobile' },
    warrener = { model = 'warrener',      name = 'Warrener',                      brand = 'Vulcar',             category = 'sedans',         type = 'automobile' },
    washington = { model = 'washington',    name = 'Washington',                    brand = 'Albany',             category = 'sedans',         type = 'automobile' },
    tailgater2 = { model = 'tailgater2',    name = 'Tailgater S',                   brand = 'Obey',              category = 'sedans',         type = 'automobile' },
    cinquemila = { model = 'cinquemila',    name = 'Lampadati',                     brand = 'Cinquemila',       category = 'sedans',         type = 'automobile' },
    iwagen = { model = 'iwagen',        name = 'Obey',                          brand = 'I-Wagen',          category = 'sedans',         type = 'automobile' },
    astron = { model = 'astron',        name = 'Astron',                        brand = 'Pfister',          category = 'sedans',         type = 'automobile' },
    baller7 = { model = 'baller7',       name = 'Baller ST',                     brand = 'Gallivanter',      category = 'sedans',         type = 'automobile' },
    comet7 = { model = 'comet7',        name = 'Comet',                         brand = 'S2 Cabrio',         category = 'sedans',         type = 'automobile' },
    deity = { model = 'deity',         name = 'Deity',                         brand = 'Enus',             category = 'sedans',         type = 'automobile' },
    jubilee = { model = 'jubilee',       name = 'Jubilee',                       brand = 'Enus',             category = 'sedans',         type = 'automobile' },
    oracle = { model = 'oracle',        name = 'Oracle',                        brand = 'Übermacht',         category = 'sedans',         type = 'automobile' },
    schafter2 = { model = 'schafter2',     name = 'Schafter',                      brand = 'Benefactor',        category = 'sedans',         type = 'automobile' },
    warrener2 = { model = 'warrener2',     name = 'Warrener HKR',                  brand = 'Vulcar',            category = 'sedans',         type = 'automobile' },
    rhinehart = { model = 'rhinehart',     name = 'Rhinehart',                     brand = 'Übermacht',        category = 'sedans',         type = 'automobile' },
    eudora = { model = 'eudora',        name = 'Eudora',                        brand = 'Willard',           category = 'sedans',         type = 'automobile' },
    asterope2 = { model = 'asterope2',     name = 'Asterope GZ',                   brand = 'Karin',             category = 'sedans',        type = 'automobile' },
    --- SUV (2)
    baller = { model = 'baller',        name = 'Baller',                        brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    baller2 = { model = 'baller2',       name = 'Baller II',                     brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    baller3 = { model = 'baller3',       name = 'Baller LE',                     brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    baller4 = { model = 'baller4',       name = 'Baller LE LWB',                 brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    baller5 = { model = 'baller5',       name = 'Baller LE (Armored)',           brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    baller6 = { model = 'baller6',       name = 'Baller LE LWB (Armored)',       brand = 'Gallivanter',       category = 'suvs',           type = 'automobile' },
    bjxl = { model = 'bjxl',          name = 'BeeJay XL',                     brand = 'Karin',             category = 'suvs',           type = 'automobile' },
    cavalcade = { model = 'cavalcade',     name = 'Cavalcade',                     brand = 'Albany',            category = 'suvs',           type = 'automobile' },
    cavalcade2 = { model = 'cavalcade2',    name = 'Cavalcade II',                  brand = 'Albany',            category = 'suvs',           type = 'automobile' },
    contender = { model = 'contender',     name = 'Contender',                     brand = 'Vapid',             category = 'suvs',           type = 'automobile' },
    dubsta = { model = 'dubsta',        name = 'Dubsta',                        brand = 'Benefactor',        category = 'suvs',           type = 'automobile' },
    dubsta2 = { model = 'dubsta2',       name = 'Dubsta Luxury',                 brand = 'Benefactor',        category = 'suvs',           type = 'automobile' },
    fq2 = { model = 'fq2',           name = 'FQ2',                           brand = 'Fathom',            category = 'suvs',           type = 'automobile' },
    granger = { model = 'granger',       name = 'Granger',                       brand = 'Declasse',          category = 'suvs',           type = 'automobile' },
    gresley = { model = 'gresley',       name = 'Gresley',                       brand = 'Bravado',           category = 'suvs',           type = 'automobile' },
    habanero = { model = 'habanero',      name = 'Habanero',                      brand = 'Emperor',           category = 'suvs',           type = 'automobile' },
    huntley = { model = 'huntley',       name = 'Huntley S',                     brand = 'Enus',              category = 'suvs',           type = 'automobile' },
    landstalker = { model = 'landstalker',   name = 'Landstalker',                   brand = 'Dundreary',         category = 'suvs',           type = 'automobile' },
    landstalker2 = { model = 'landstalker2',  name = 'Landstalker XL',                brand = 'Dundreary',         category = 'suvs',           type = 'automobile' },
    novak = { model = 'novak',         name = 'Novak',                         brand = 'Lampadati',         category = 'suvs',           type = 'automobile' },
    patriot = { model = 'patriot',       name = 'Patriot',                       brand = 'Mammoth',           category = 'suvs',           type = 'automobile' },
    patriot2 = { model = 'patriot2',      name = 'Patriot Stretch',               brand = 'Mammoth',           category = 'suvs',           type = 'automobile' },
    radi = { model = 'radi',          name = 'Radius',                        brand = 'Vapid',             category = 'suvs',           type = 'automobile' },
    rebla = { model = 'rebla',         name = 'Rebla GTS',                     brand = 'Übermacht',         category = 'suvs',           type = 'automobile' },
    rocoto = { model = 'rocoto',        name = 'Rocoto',                        brand = 'Obey',              category = 'suvs',           type = 'automobile' },
    seminole = { model = 'seminole',      name = 'Seminole',                      brand = 'Canis',             category = 'suvs',           type = 'automobile' },
    seminole2 = { model = 'seminole2',     name = 'Seminole Frontier',             brand = 'Canis',             category = 'suvs',           type = 'automobile' },
    serrano = { model = 'serrano',       name = 'Serrano',                       brand = 'Benefactor',        category = 'suvs',           type = 'automobile' },
    toros = { model = 'toros',         name = 'Toros',                         brand = 'Pegassi',           category = 'suvs',           type = 'automobile' },
    xls = { model = 'xls',           name = 'XLS',                           brand = 'Benefactor',        category = 'suvs',           type = 'automobile' },
    granger2 = { model = 'granger2',      name = 'Granger 3600LX',                brand = 'Declasse',         category = 'suvs',           type = 'automobile' },
    patriot3 = { model = 'patriot3',      name = 'Patriot Military',              brand = 'Mil-Spec',         category = 'suvs',           type = 'automobile' },
    aleutian = { model = 'aleutian',      name = 'Aleutian',                      brand = 'Vapid',            category = 'suvs',           type = 'automobile' },
    baller8 = { model = 'baller8',       name = 'Baller ST-D',                   brand = 'Gallivanter',      category = 'suvs',           type = 'automobile' },
    cavalcade3 = { model = 'cavalcade3',    name = 'Cavalcade XL',                  brand = 'Albany',           category = 'suvs',           type = 'automobile' },
    dorado = { model = 'dorado',        name = 'Dorado',                        brand = 'Bravado',          category = 'suvs',           type = 'automobile' },
    vivanite = { model = 'vivanite',      name = 'Vivanite',                      brand = 'Karin',            category = 'suvs',           type = 'automobile' },
    --- Coupes (3)
    cogcabrio = { model = 'cogcabrio',     name = 'Cognoscenti Cabrio',            brand = 'Enus',              category = 'coupes',         type = 'automobile' },
    exemplar = { model = 'exemplar',      name = 'Exemplar',                      brand = 'Dewbauchee',        category = 'coupes',         type = 'automobile' },
    f620 = { model = 'f620',          name = 'F620',                          brand = 'Ocelot',            category = 'coupes',         type = 'automobile' },
    felon = { model = 'felon',         name = 'Felon',                         brand = 'Lampadati',         category = 'coupes',         type = 'automobile' },
    felon2 = { model = 'felon2',        name = 'Felon GT',                      brand = 'Lampadati',         category = 'coupes',         type = 'automobile' },
    jackal = { model = 'jackal',        name = 'Jackal',                        brand = 'Ocelot',            category = 'coupes',         type = 'automobile' },
    oracle2 = { model = 'oracle2',       name = 'Oracle XS',                     brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    sentinel = { model = 'sentinel',      name = 'Sentinel',                      brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    sentinel2 = { model = 'sentinel2',     name = 'Sentinel XS',                   brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    windsor = { model = 'windsor',       name = 'Windsor',                       brand = 'Enus',              category = 'coupes',         type = 'automobile' },
    windsor2 = { model = 'windsor2',      name = 'Windsor Drop',                  brand = 'Enus',              category = 'coupes',         type = 'automobile' },
    zion = { model = 'zion',          name = 'Zion',                          brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    zion2 = { model = 'zion2',         name = 'Zion Cabrio',                   brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    previon = { model = 'previon',       name = 'Previon',                       brand = 'Karin',            category = 'coupes',         type = 'automobile' },
    champion = { model = 'champion',      name = 'Champion',                      brand = 'Dewbauchee',       category = 'coupes',         type = 'automobile' },
    futo = { model = 'futo',          name = 'Futo',                          brand = 'Karin',             category = 'coupes',         type = 'automobile' },
    sentinel3 = { model = 'sentinel3',     name = 'Sentinel Classic',              brand = 'Übermacht',         category = 'coupes',         type = 'automobile' },
    kanjosj = { model = 'kanjosj',       name = 'Kanjo SJ',                      brand = 'Dinka',            category = 'coupes',         type = 'automobile' },
    postlude = { model = 'postlude',      name = 'Postlude',                      brand = 'Dinka',             category = 'coupes',         type = 'automobile' },
    tahoma = { model = 'tahoma',        name = 'Tahoma Coupe',                  brand = 'Declasse',          category = 'coupes',         type = 'automobile' },
    broadway = { model = 'broadway',      name = 'Broadway',                      brand = 'Classique',         category = 'coupes',         type = 'automobile' },
    fr36 = { model = 'fr36',          name = 'FR36',                          brand = 'Fathom',            category = 'coupes',        type = 'automobile' },
    --- Muscle (4)
    blade = { model = 'blade',         name = 'Blade',                         brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    buccaneer = { model = 'buccaneer',     name = 'Buccaneer',                     brand = 'Albany',            category = 'muscle',         type = 'automobile' },
    buccaneer2 = { model = 'buccaneer2',    name = 'Buccaneer Rider',               brand = 'Albany',            category = 'muscle',         type = 'automobile' },
    chino = { model = 'chino',         name = 'Chino',                         brand = 'Vapid',              category = 'muscle',         type = 'automobile' },
    chino2 = { model = 'chino2',        name = 'Chino Luxe',                    brand = 'Vapid',              category = 'muscle',         type = 'automobile' },
    clique = { model = 'clique',        name = 'Clique',                        brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    coquette3 = { model = 'coquette3',     name = 'Coquette BlackFin',             brand = 'Invetero',         category = 'muscle',         type = 'automobile' },
    deviant = { model = 'deviant',       name = 'Deviant',                       brand = 'Schyster',          category = 'muscle',         type = 'automobile' },
    dominator = { model = 'dominator',     name = 'Dominator',                     brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    dominator2 = { model = 'dominator2',    name = 'Pißwasser Dominator',           brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    dominator3 = { model = 'dominator3',    name = 'Dominator GTX',                 brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    dominator4 = { model = 'dominator4',    name = 'Dominator Arena',               brand = 'Vapid',            category = 'muscle',         type = 'automobile' },
    dominator7 = { model = 'dominator7',    name = 'Dominator ASP',                 brand = 'Vapid',            category = 'muscle',         type = 'automobile' },
    dominator8 = { model = 'dominator8',    name = 'Dominator GTT',                 brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    dukes = { model = 'dukes',         name = 'Dukes',                         brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    dukes2 = { model = 'dukes2',        name = 'Duke O\'Death',                 brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    dukes3 = { model = 'dukes3',        name = 'Beater Dukes',                  brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    faction = { model = 'faction',       name = 'Faction',                       brand = 'Willard',           category = 'muscle',         type = 'automobile' },
    faction2 = { model = 'faction2',      name = 'Faction Rider',                 brand = 'Willard',           category = 'muscle',         type = 'automobile' },
    faction3 = { model = 'faction3',      name = 'Faction Custom Donk',           brand = 'Willard',           category = 'muscle',         type = 'automobile' },
    ellie = { model = 'ellie',         name = 'Ellie',                         brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    gauntlet = { model = 'gauntlet',      name = 'Gauntlet',                      brand = 'Bravado',           category = 'muscle',         type = 'automobile' },
    gauntlet2 = { model = 'gauntlet2',     name = 'Redwood Gauntlet',              brand = 'Bravado',           category = 'muscle',         type = 'automobile' },
    gauntlet3 = { model = 'gauntlet3',     name = 'Classic Gauntlet',              brand = 'Bravado',           category = 'muscle',         type = 'automobile' },
    gauntlet4 = { model = 'gauntlet4',     name = 'Gauntlet Hellfire',             brand = 'Bravado',           category = 'muscle',         type = 'automobile' },
    gauntlet5 = { model = 'gauntlet5',     name = 'Gauntlet Classic Custom',       brand = 'Bravado',          category = 'muscle',         type = 'automobile' },
    hermes = { model = 'hermes',        name = 'Hermes',                        brand = 'Albany',           category = 'muscle',         type = 'automobile' },
    hotknife = { model = 'hotknife',      name = 'Hotknife',                      brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    hustler = { model = 'hustler',       name = 'Hustler',                       brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    impaler = { model = 'impaler',       name = 'Impaler',                       brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    impaler2 = { model = 'impaler2',      name = 'Impaler Arena',                 brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    impaler3 = { model = 'impaler3',      name = 'Impaler Future Shock',          brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    impaler4 = { model = 'impaler4',      name = 'Impaler Nightmare',             brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    imperator = { model = 'imperator',     name = 'Imperator Arena',               brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    imperator2 = { model = 'imperator2',    name = 'imperator Future Shock',        brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    imperator3 = { model = 'imperator3',    name = 'Imperator Nightmare',           brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    lurcher = { model = 'lurcher',       name = 'Lurcher',                       brand = 'Bravado',           category = 'muscle',         type = 'automobile' },
    nightshade = { model = 'nightshade',    name = 'Nightshade',                    brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    phoenix = { model = 'phoenix',       name = 'Phoenix',                       brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    picador = { model = 'picador',       name = 'Picador',                       brand = 'Cheval',            category = 'muscle',         type = 'automobile' },
    ratloader2 = { model = 'ratloader2',    name = 'Ratloader',                     brand = 'Ratloader2',        category = 'muscle',         type = 'automobile' },
    ruiner = { model = 'ruiner',        name = 'Ruiner',                        brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    ruiner2 = { model = 'ruiner2',       name = 'Ruiner 2000',                   brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    sabregt = { model = 'sabregt',       name = 'Sabre GT Turbo',                brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    sabregt2 = { model = 'sabregt2',      name = 'Sabre GT Turbo Custom',         brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    slamvan = { model = 'slamvan',       name = 'Slam Van',                      brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    slamvan2 = { model = 'slamvan2',      name = 'Lost Slam Van',                 brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    slamvan3 = { model = 'slamvan3',      name = 'Slam Van Custom',               brand = 'Vapid',             category = 'muscle',         type = 'automobile' },
    stalion = { model = 'stalion',       name = 'Stallion',                      brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    stalion2 = { model = 'stalion2',      name = 'Stallion Burgershot',           brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    tampa = { model = 'tampa',         name = 'Tampa',                         brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    tulip = { model = 'tulip',         name = 'Tulip',                         brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    vamos = { model = 'vamos',         name = 'Vamos',                         brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    vigero = { model = 'vigero',        name = 'Vigero',                        brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    virgo = { model = 'virgo',         name = 'Virgo',                         brand = 'Albany',            category = 'muscle',         type = 'automobile' },
    virgo2 = { model = 'virgo2',        name = 'Virgo Custom Classic',          brand = 'Dundreary',         category = 'muscle',         type = 'automobile' },
    virgo3 = { model = 'virgo3',        name = 'Virgo Classic',                 brand = 'Dundreary',         category = 'muscle',         type = 'automobile' },
    voodoo = { model = 'voodoo',        name = 'Voodoo',                        brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    yosemite = { model = 'yosemite',      name = 'Yosemite',                      brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    yosemite2 = { model = 'yosemite2',     name = 'Yosemite Drift',                brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    buffalo4 = { model = 'buffalo4',      name = 'Buffalo STX',                   brand = 'Bravado',          category = 'muscle',         type = 'automobile' },
    manana = { model = 'manana',        name = 'Manana',                        brand = 'Albany',            category = 'muscle',         type = 'automobile' },
    manana2 = { model = 'manana2',       name = 'Manana Custom',                 brand = 'Albany',            category = 'muscle',         type = 'automobile' },
    tampa2 = { model = 'tampa2',        name = 'Drift Tampa',                   brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    ruiner4 = { model = 'ruiner4',       name = 'Ruiner ZZ-8',                   brand = 'Imponte',           category = 'muscle',         type = 'automobile' },
    vigero2 = { model = 'vigero2',       name = 'Vigero ZX',                     brand = 'Declasse',         category = 'muscle',         type = 'automobile' },
    weevil2 = { model = 'weevil2',       name = 'Weevil Custom',                 brand = 'BF',                category = 'muscle',         type = 'automobile' },
    buffalo5 = { model = 'buffalo5',      name = 'Buffalo EVX',                   brand = 'Bravado',          category = 'muscle',         type = 'automobile' },
    tulip2 = { model = 'tulip2',        name = 'Tulip M-100',                   brand = 'Declasse',          category = 'muscle',         type = 'automobile' },
    clique2 = { model = 'clique2',       name = 'Clique Wagon',                  brand = 'Vapid',            category = 'muscle',         type = 'automobile' },
    brigham = { model = 'brigham',       name = 'Brigham',                       brand = 'Albany',           category = 'muscle',         type = 'automobile' },
    greenwood = { model = 'greenwood',     name = 'Greenwood',                     brand = 'Bravado',          category = 'muscle',         type = 'automobile' },
    dominator9 = { model = 'dominator9',    name = 'Dominator GT',                  brand = 'Vapid',            category = 'muscle',         type = 'automobile' },
    impaler6 = { model = 'impaler6',      name = 'Impaler LX',                    brand = 'Declasse',         category = 'muscle',         type = 'automobile' },
    vigero3 = { model = 'vigero3',       name = 'Vigero ZX Convertible',         brand = 'Declasse',         category = 'muscle',         type = 'automobile' },
    --- Sports Classic (5)
    ardent = { model = 'ardent',        name = 'Ardent',                        brand = 'Ocelot',            category = 'sportsclassics', type = 'automobile' },
    btype = { model = 'btype',         name = 'Roosevelt',                     brand = 'Albany',            category = 'sportsclassics', type = 'automobile' },
    btype2 = { model = 'btype2',        name = 'Franken Stange',                brand = 'Albany',            category = 'sportsclassics', type = 'automobile' },
    btype3 = { model = 'btype3',        name = 'Roosevelt Valor',               brand = 'Albany',            category = 'sportsclassics', type = 'automobile' },
    casco = { model = 'casco',         name = 'Casco',                         brand = 'Lampadati',        category = 'sportsclassics', type = 'automobile' },
    deluxo = { model = 'deluxo',        name = 'Deluxo',                        brand = 'Imponte',           category = 'sportsclassics', type = 'automobile' },
    dynasty = { model = 'dynasty',       name = 'Dynasty',                       brand = 'Weeny',             category = 'sportsclassics', type = 'automobile' },
    fagaloa = { model = 'fagaloa',       name = 'Fagaloa',                       brand = 'Vulcar',            category = 'sportsclassics', type = 'automobile' },
    feltzer3 = { model = 'feltzer3',      name = 'Stirling GT',                   brand = 'Benefactor',       category = 'sportsclassics', type = 'automobile' },
    gt500 = { model = 'gt500',         name = 'GT500',                         brand = 'Grotti',           category = 'sportsclassics', type = 'automobile' },
    infernus2 = { model = 'infernus2',     name = 'Infernus Classic',              brand = 'Pegassi',          category = 'sportsclassics', type = 'automobile' },
    jb700 = { model = 'jb700',         name = 'JB 700',                        brand = 'Dewbauchee',       category = 'sportsclassics', type = 'automobile' },
    jb7002 = { model = 'jb7002',        name = 'JB 700W',                       brand = 'Dewbauchee',        category = 'sportsclassics', type = 'automobile' },
    mamba = { model = 'mamba',         name = 'Mamba',                         brand = 'Declasse',         category = 'sportsclassics', type = 'automobile' },
    michelli = { model = 'michelli',      name = 'Michelli GT',                   brand = 'Lampadati',         category = 'sportsclassics', type = 'automobile' },
    monroe = { model = 'monroe',        name = 'Monroe',                        brand = 'Pegassi',          category = 'sportsclassics', type = 'automobile' },
    nebula = { model = 'nebula',        name = 'Nebula',                        brand = 'Vulcar',            category = 'sportsclassics', type = 'automobile' },
    peyote = { model = 'peyote',        name = 'Peyote',                        brand = 'Vapid',             category = 'sportsclassics', type = 'automobile' },
    peyote3 = { model = 'peyote3',       name = 'Peyote Custom',                 brand = 'Vapid',             category = 'sportsclassics', type = 'automobile' },
    pigalle = { model = 'pigalle',       name = 'Pigalle',                       brand = 'Lampadati',         category = 'sportsclassics', type = 'automobile' },
    rapidgt3 = { model = 'rapidgt3',      name = 'Rapid GT Classic',              brand = 'Dewbauchee',        category = 'sportsclassics', type = 'automobile' },
    retinue = { model = 'retinue',       name = 'Retinue',                       brand = 'Vapid',             category = 'sportsclassics', type = 'automobile' },
    retinue2 = { model = 'retinue2',      name = 'Retinue MKII',                  brand = 'Vapid',             category = 'sportsclassics', type = 'automobile' },
    savestra = { model = 'savestra',      name = 'Savestra',                      brand = 'Annis',             category = 'sportsclassics', type = 'automobile' },
    stinger = { model = 'stinger',       name = 'Stinger',                       brand = 'Grotti',            category = 'sportsclassics', type = 'automobile' },
    stingergt = { model = 'stingergt',     name = 'Stinger GT',                    brand = 'Grotti',            category = 'sportsclassics', type = 'automobile' },
    stromberg = { model = 'stromberg',     name = 'Stromberg',                     brand = 'Ocelot',            category = 'sportsclassics', type = 'automobile' },
    swinger = { model = 'swinger',       name = 'Swinger',                       brand = 'Ocelot',           category = 'sportsclassics', type = 'automobile' },
    torero = { model = 'torero',        name = 'Torero',                        brand = 'Pegassi',           category = 'sportsclassics', type = 'automobile' },
    tornado = { model = 'tornado',       name = 'Tornado',                       brand = 'Declasse',          category = 'sportsclassics', type = 'automobile' },
    tornado2 = { model = 'tornado2',      name = 'Tornado Convertible',           brand = 'Declasse',          category = 'sportsclassics', type = 'automobile' },
    tornado5 = { model = 'tornado5',      name = 'Tornado Custom',                brand = 'Declasse',          category = 'sportsclassics', type = 'automobile' },
    turismo2 = { model = 'turismo2',      name = 'Turismo Classic',               brand = 'Grotti',           category = 'sportsclassics', type = 'automobile' },
    viseris = { model = 'viseris',       name = 'Viseris',                       brand = 'Lampadati',        category = 'sportsclassics', type = 'automobile' },
    z190 = { model = 'z190',          name = '190Z',                          brand = 'Karin',             category = 'sportsclassics', type = 'automobile' },
    ztype = { model = 'ztype',         name = 'Z-Type',                        brand = 'Truffade',         category = 'sportsclassics', type = 'automobile' },
    zion3 = { model = 'zion3',         name = 'Zion Classic',                  brand = 'Übermacht',         category = 'sportsclassics', type = 'automobile' },
    cheburek = { model = 'cheburek',      name = 'Cheburek',                      brand = 'Rune',               category = 'sportsclassics', type = 'automobile' },
    toreador = { model = 'toreador',      name = 'Toreador',                      brand = 'Pegassi',           category = 'sportsclassics', type = 'automobile' },
    peyote2 = { model = 'peyote2',       name = 'Peyote Gasser',                 brand = 'Vapid',             category = 'sportsclassics', type = 'automobile' },
    coquette2 = { model = 'coquette2',     name = 'Coquette Classic',              brand = 'Invetero',         category = 'sportsclassics', type = 'automobile' },
    --- Sports (6)
    alpha = { model = 'alpha',         name = 'Alpha',                         brand = 'Albany',            category = 'sports',         type = 'automobile' },
    banshee = { model = 'banshee',       name = 'Banshee',                       brand = 'Bravado',           category = 'sports',         type = 'automobile' },
    bestiagts = { model = 'bestiagts',     name = 'Bestia GTS',                    brand = 'Grotti',            category = 'sports',         type = 'automobile' },
    buffalo = { model = 'buffalo',       name = 'Buffalo',                       brand = 'Bravado',           category = 'sports',         type = 'automobile' },
    buffalo2 = { model = 'buffalo2',      name = 'Buffalo S',                     brand = 'Bravado',           category = 'sports',         type = 'automobile' },
    carbonizzare = { model = 'carbonizzare',  name = 'Carbonizzare',                  brand = 'Grotti',           category = 'sports',         type = 'automobile' },
    comet2 = { model = 'comet2',        name = 'Comet',                         brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    comet3 = { model = 'comet3',        name = 'Comet Retro Custom',            brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    comet4 = { model = 'comet4',        name = 'Comet Safari',                  brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    comet5 = { model = 'comet5',        name = 'Comet SR',                      brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    coquette = { model = 'coquette',      name = 'Coquette',                      brand = 'Invetero',         category = 'sports',         type = 'automobile' },
    coquette4 = { model = 'coquette4',     name = 'Coquette D10',                  brand = 'Invetero',         category = 'sports',         type = 'automobile' },
    drafter = { model = 'drafter',       name = '8F Drafter',                    brand = 'Obey',              category = 'sports',         type = 'automobile' },
    elegy = { model = 'elegy',         name = 'Elegy Retro Custom',            brand = 'Annis',            category = 'sports',         type = 'automobile' },
    elegy2 = { model = 'elegy2',        name = 'Elegy RH8',                     brand = 'Annis',            category = 'sports',         type = 'automobile' },
    feltzer2 = { model = 'feltzer2',      name = 'Feltzer',                       brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    flashgt = { model = 'flashgt',       name = 'Flash GT',                      brand = 'Vapid',             category = 'sports',         type = 'automobile' },
    furoregt = { model = 'furoregt',      name = 'Furore GT',                     brand = 'Lampadati',         category = 'sports',         type = 'automobile' },
    gb200 = { model = 'gb200',         name = 'GB 200',                        brand = 'Vapid',            category = 'sports',         type = 'automobile' },
    komoda = { model = 'komoda',        name = 'Komoda',                        brand = 'Lampadati',         category = 'sports',         type = 'automobile' },
    imorgon = { model = 'imorgon',       name = 'Imorgon',                       brand = 'Överflöd',         category = 'sports',         type = 'automobile' },
    italigto = { model = 'italigto',      name = 'Itali GTO',                     brand = 'Progen',           category = 'sports',         type = 'automobile' },
    jugular = { model = 'jugular',       name = 'Jugular',                       brand = 'Ocelot',            category = 'sports',         type = 'automobile' },
    jester = { model = 'jester',        name = 'Jester',                        brand = 'Dinka',            category = 'sports',         type = 'automobile' },
    jester2 = { model = 'jester2',       name = 'Jester Racecar',                brand = 'Dinka',            category = 'sports',         type = 'automobile' },
    jester3 = { model = 'jester3',       name = 'Jester Classic',                brand = 'Dinka',             category = 'sports',         type = 'automobile' },
    khamelion = { model = 'khamelion',     name = 'Khamelion',                     brand = 'Hijak',             category = 'sports',         type = 'automobile' },
    kuruma = { model = 'kuruma',        name = 'Kuruma',                        brand = 'Karin',             category = 'sports',         type = 'automobile' },
    kuruma2 = { model = 'kuruma2',       name = 'kuruma2',                       brand = 'Karin2',            category = 'sports',         type = 'automobile' },
    locust = { model = 'locust',        name = 'Locust',                        brand = 'Ocelot',           category = 'sports',         type = 'automobile' },
    lynx = { model = 'lynx',          name = 'Lynx',                          brand = 'Ocelot',           category = 'sports',         type = 'automobile' },
    massacro = { model = 'massacro',      name = 'Massacro',                      brand = 'Dewbauchee',       category = 'sports',         type = 'automobile' },
    massacro2 = { model = 'massacro2',     name = 'Massacro Racecar',              brand = 'Dewbauchee',        category = 'sports',         type = 'automobile' },
    neo = { model = 'neo',           name = 'Neo',                           brand = 'Vysser',           category = 'sports',         type = 'automobile' },
    neon = { model = 'neon',          name = 'Neon',                          brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    ninef = { model = 'ninef',         name = '9F',                            brand = 'Obey',              category = 'sports',         type = 'automobile' },
    ninef2 = { model = 'ninef2',        name = '9F Cabrio',                     brand = 'Obey',             category = 'sports',         type = 'automobile' },
    omnis = { model = 'omnis',         name = 'Omnis',                         brand = 'Wow',               category = 'sports',         type = 'automobile' },
    paragon = { model = 'paragon',       name = 'Paragon',                       brand = 'Enus',              category = 'sports',         type = 'automobile' },
    pariah = { model = 'pariah',        name = 'Pariah',                        brand = 'Ocelot',            category = 'sports',         type = 'automobile' },
    penumbra = { model = 'penumbra',      name = 'Penumbra',                      brand = 'Maibatsu',          category = 'sports',         type = 'automobile' },
    penumbra2 = { model = 'penumbra2',     name = 'Penumbra FF',                   brand = 'Maibatsu',          category = 'sports',         type = 'automobile' },
    rapidgt = { model = 'rapidgt',       name = 'Rapid GT',                      brand = 'Dewbauchee',        category = 'sports',         type = 'automobile' },
    rapidgt2 = { model = 'rapidgt2',      name = 'Rapid GT Convertible',          brand = 'Dewbauchee',        category = 'sports',         type = 'automobile' },
    raptor = { model = 'raptor',        name = 'Raptor',                        brand = 'BF',                category = 'sports',         type = 'automobile' },
    revolter = { model = 'revolter',      name = 'Revolter',                      brand = 'Übermacht',         category = 'sports',         type = 'automobile' },
    ruston = { model = 'ruston',        name = 'Ruston',                        brand = 'Hijak',            category = 'sports',         type = 'automobile' },
    schafter3 = { model = 'schafter3',     name = 'Schafter V12',                  brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    schafter4 = { model = 'schafter4',     name = 'Schafter LWB',                  brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    schlagen = { model = 'schlagen',      name = 'Schlagen GT',                   brand = 'Benefactor',       category = 'sports',         type = 'automobile' },
    schwarzer = { model = 'schwarzer',     name = 'Schwartzer',                    brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    seven70 = { model = 'seven70',       name = 'Seven-70',                      brand = 'Dewbauchee',       category = 'sports',         type = 'automobile' },
    specter = { model = 'specter',       name = 'Specter',                       brand = 'Dewbauchee',       category = 'sports',         type = 'automobile' },
    streiter = { model = 'streiter',      name = 'Streiter',                      brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    sugoi = { model = 'sugoi',         name = 'Sugoi',                         brand = 'Dinka',             category = 'sports',         type = 'automobile' },
    sultan = { model = 'sultan',        name = 'Sultan',                        brand = 'Karin',             category = 'sports',         type = 'automobile' },
    sultan2 = { model = 'sultan2',       name = 'Sultan Custom',                 brand = 'Karin',             category = 'sports',         type = 'automobile' },
    surano = { model = 'surano',        name = 'Surano',                        brand = 'Benefactor',        category = 'sports',         type = 'automobile' },
    tropos = { model = 'tropos',        name = 'Tropos Rallye',                 brand = 'Lampadati',         category = 'sports',         type = 'automobile' },
    verlierer2 = { model = 'verlierer2',    name = 'Verlierer',                     brand = 'Bravado',           category = 'sports',         type = 'automobile' },
    vstr = { model = 'vstr',          name = 'V-STR',                         brand = 'Albany',            category = 'sports',         type = 'automobile' },
    italirsx = { model = 'italirsx',      name = 'Itali RSX',                     brand = 'Progen',           category = 'sports',         type = 'automobile' },
    zr350 = { model = 'zr350',         name = 'ZR350',                         brand = 'Annis',             category = 'sports',         type = 'automobile' },
    calico = { model = 'calico',        name = 'Calico GTF',                    brand = 'Karin',             category = 'sports',         type = 'automobile' },
    futo2 = { model = 'futo2',         name = 'Futo GTX',                      brand = 'Karin',             category = 'sports',         type = 'automobile' },
    euros = { model = 'euros',         name = 'Euros',                         brand = 'Annis',             category = 'sports',         type = 'automobile' },
    jester4 = { model = 'jester4',       name = 'Jester RR',                     brand = 'Dinka',            category = 'sports',         type = 'automobile' },
    remus = { model = 'remus',         name = 'Remus',                         brand = 'Annis',             category = 'sports',         type = 'automobile' },
    comet6 = { model = 'comet6',        name = 'Comet S2',                      brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    growler = { model = 'growler',       name = 'Growler',                       brand = 'Pfister',          category = 'sports',         type = 'automobile' },
    vectre = { model = 'vectre',        name = 'Vectre',                        brand = 'Emperor',           category = 'sports',         type = 'automobile' },
    cypher = { model = 'cypher',        name = 'Cypher',                        brand = 'Übermacht',        category = 'sports',         type = 'automobile' },
    sultan3 = { model = 'sultan3',       name = 'Sultan Classic Custom',         brand = 'Karin',             category = 'sports',         type = 'automobile' },
    rt3000 = { model = 'rt3000',        name = 'RT3000',                        brand = 'Dinka',             category = 'sports',         type = 'automobile' },
    sultanrs = { model = 'sultanrs',      name = 'Sultan RS',                     brand = 'Karin',             category = 'sports',         type = 'automobile' },
    visione = { model = 'visione',       name = 'Visione',                       brand = 'Grotti',           category = 'sports',         type = 'automobile' },
    cheetah2 = { model = 'cheetah2',      name = 'Cheetah Classic',               brand = 'Grotti',           category = 'sports',         type = 'automobile' },
    stingertt = { model = 'stingertt',     name = 'Itali GTO Stinger TT',          brand = 'Maibatsu',         category = 'sports',         type = 'automobile' },
    omnisegt = { model = 'omnisegt',      name = 'Omnis e-GT',                    brand = 'Obey',             category = 'sports',         type = 'automobile' },
    sentinel4 = { model = 'sentinel4',     name = 'Sentinel Classic Widebody',     brand = 'Übermacht',        category = 'sports',         type = 'automobile' },
    sm722 = { model = 'sm722',         name = 'SM722',                         brand = 'Benefactor',       category = 'sports',         type = 'automobile' },
    tenf = { model = 'tenf',          name = '10F',                           brand = 'Obey',             category = 'sports',         type = 'automobile' },
    tenf2 = { model = 'tenf2',         name = '10F Widebody',                  brand = 'Obey',             category = 'sports',         type = 'automobile' },
    everon2 = { model = 'everon2',       name = 'Everon Hotring',                brand = 'Karin',             category = 'sports',         type = 'automobile' },
    issi8 = { model = 'issi8',         name = 'Issi Rally',                    brand = 'Weeny',             category = 'sports',         type = 'automobile' },
    corsita = { model = 'corsita',       name = 'Corsita',                       brand = 'Lampadati',         category = 'sports',         type = 'automobile' },
    gauntlet6 = { model = 'gauntlet6',     name = 'Hotring Hellfire',              brand = 'Bravado',          category = 'sports',         type = 'automobile' },
    coureur = { model = 'coureur',       name = 'La Coureuse',                   brand = 'Penaud',           category = 'sports',         type = 'automobile' },
    r300 = { model = 'r300',          name = '300R',                          brand = 'Annis',             category = 'sports',         type = 'automobile' },
    panthere = { model = 'panthere',      name = 'Panthere',                      brand = 'Toundra',           category = 'sports',         type = 'automobile' },
    --- Super (7)
    adder = { model = 'adder',         name = 'Adder',                         brand = 'Truffade',         category = 'super',          type = 'automobile' },
    autarch = { model = 'autarch',       name = 'Autarch',                       brand = 'Överflöd',         category = 'super',          type = 'automobile' },
    banshee2 = { model = 'banshee2',      name = 'Banshee 900R',                  brand = 'Bravado',          category = 'super',          type = 'automobile' },
    bullet = { model = 'bullet',        name = 'Bullet',                        brand = 'Vapid',            category = 'super',          type = 'automobile' },
    cheetah = { model = 'cheetah',       name = 'Cheetah',                       brand = 'Grotti',           category = 'super',          type = 'automobile' },
    cyclone = { model = 'cyclone',       name = 'Cyclone',                       brand = 'Coil',             category = 'super',          type = 'automobile' },
    entity2 = { model = 'entity2',       name = 'Entity XXR',                    brand = 'Överflöd',         category = 'super',          type = 'automobile' },
    entityxf = { model = 'entityxf',      name = 'Entity XF',                     brand = 'Överflöd',         category = 'super',          type = 'automobile' },
    emerus = { model = 'emerus',        name = 'Emerus',                        brand = 'Progen',           category = 'super',          type = 'automobile' },
    fmj = { model = 'fmj',           name = 'FMJ',                           brand = 'Vapid',            category = 'super',          type = 'automobile' },
    furia = { model = 'furia',         name = 'Furia',                         brand = 'Grotti',           category = 'super',          type = 'automobile' },
    gp1 = { model = 'gp1',           name = 'GP1',                           brand = 'Progen',           category = 'super',          type = 'automobile' },
    infernus = { model = 'infernus',      name = 'Infernus',                      brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    italigtb = { model = 'italigtb',      name = 'Itali GTB',                     brand = 'Progen',           category = 'super',          type = 'automobile' },
    italigtb2 = { model = 'italigtb2',     name = 'Itali GTB Custom',              brand = 'Progen',           category = 'super',          type = 'automobile' },
    krieger = { model = 'krieger',       name = 'Krieger',                       brand = 'Benefactor',       category = 'super',          type = 'automobile' },
    le7b = { model = 'le7b',          name = 'RE-7B',                         brand = 'Annis',            category = 'super',          type = 'automobile' },
    nero = { model = 'nero',          name = 'Nero',                          brand = 'Truffade',         category = 'super',          type = 'automobile' },
    nero2 = { model = 'nero2',         name = 'Nero Custom',                   brand = 'Truffade',         category = 'super',          type = 'automobile' },
    osiris = { model = 'osiris',        name = 'Osiris',                        brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    penetrator = { model = 'penetrator',    name = 'Penetrator',                    brand = 'Ocelot',           category = 'super',          type = 'automobile' },
    pfister811 = { model = 'pfister811',    name = '811',                           brand = 'Pfister',          category = 'super',          type = 'automobile' },
    prototipo = { model = 'prototipo',     name = 'X80 Proto',                     brand = 'Grotti',           category = 'super',          type = 'automobile' },
    reaper = { model = 'reaper',        name = 'Reaper',                        brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    s80 = { model = 's80',           name = 'S80RR',                         brand = 'Annis',            category = 'super',          type = 'automobile' },
    sc1 = { model = 'sc1',           name = 'SC1',                           brand = 'Übermacht',         category = 'super',          type = 'automobile' },
    sheava = { model = 'sheava',        name = 'ETR1',                          brand = 'Emperor',          category = 'super',          type = 'automobile' },
    t20 = { model = 't20',           name = 'T20',                           brand = 'Progen',          category = 'super',          type = 'automobile' },
    taipan = { model = 'taipan',        name = 'Taipan',                        brand = 'Cheval',          category = 'super',          type = 'automobile' },
    tempesta = { model = 'tempesta',      name = 'Tempesta',                      brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    tezeract = { model = 'tezeract',      name = 'Tezeract',                      brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    thrax = { model = 'thrax',         name = 'Thrax',                         brand = 'Truffade',         category = 'super',          type = 'automobile' },
    tigon = { model = 'tigon',         name = 'Tigon',                         brand = 'Lampadati',        category = 'super',          type = 'automobile' },
    turismor = { model = 'turismor',      name = 'Turismo R',                     brand = 'Grotti',           category = 'super',          type = 'automobile' },
    tyrant = { model = 'tyrant',        name = 'Tyrant',                        brand = 'Överflöd',        category = 'super',          type = 'automobile' },
    tyrus = { model = 'tyrus',         name = 'Tyrus',                         brand = 'Progen',           category = 'super',          type = 'automobile' },
    vacca = { model = 'vacca',         name = 'Vacca',                         brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    vagner = { model = 'vagner',        name = 'Vagner',                        brand = 'Dewbauchee',      category = 'super',          type = 'automobile' },
    voltic = { model = 'voltic',        name = 'Voltic',                        brand = 'Coil',             category = 'super',          type = 'automobile' },
    voltic2 = { model = 'voltic2',       name = 'Rocket Voltic',                 brand = 'Coil',            category = 'super',          type = 'automobile' },
    xa21 = { model = 'xa21',          name = 'XA-21',                         brand = 'Ocelot',           category = 'super',          type = 'automobile' },
    zentorno = { model = 'zentorno',      name = 'Zentorno',                      brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    zorrusso = { model = 'zorrusso',      name = 'Zorrusso',                      brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    ignus = { model = 'ignus',         name = 'Ignus',                         brand = 'Pegassi',         category = 'super',          type = 'automobile' },
    zeno = { model = 'zeno',          name = 'Zeno',                          brand = 'Överflöd',        category = 'super',          type = 'automobile' },
    deveste = { model = 'deveste',       name = 'Deveste',                       brand = 'Principe',         category = 'super',          type = 'automobile' },
    lm87 = { model = 'lm87',          name = 'LM87',                          brand = 'Benefactor',       category = 'super',          type = 'automobile' },
    torero2 = { model = 'torero2',       name = 'Torero XO',                     brand = 'Pegassi',          category = 'super',          type = 'automobile' },
    entity3 = { model = 'entity3',       name = 'Entity MT',                     brand = 'Overflod',         category = 'super',          type = 'automobile' },
    virtue = { model = 'virtue',        name = 'Virtue',                        brand = 'Ocelot',            category = 'super',          type = 'automobile' },
    turismo3 = { model = 'turismo3',      name = 'Turismo Omaggio',               brand = 'Grotti',            category = 'super',         type = 'automobile' },
    --- Motorcycles (8)
    akuma = { model = 'akuma',         name = 'Akuma',                         brand = 'Dinka',             category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    avarus = { model = 'avarus',        name = 'Avarus',                        brand = 'LCC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    bagger = { model = 'bagger',        name = 'Bagger',                        brand = 'WMC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    bati = { model = 'bati',          name = 'Bati 801',                      brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    bati2 = { model = 'bati2',         name = 'Bati 801RR',                    brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    bf400 = { model = 'bf400',         name = 'BF400',                         brand = 'Nagasaki',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    carbonrs = { model = 'carbonrs',      name = 'Carbon RS',                     brand = 'Nagasaki',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    chimera = { model = 'chimera',       name = 'Chimera',                       brand = 'Nagasaki',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    cliffhanger = { model = 'cliffhanger',   name = 'Cliffhanger',                   brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    daemon = { model = 'daemon',        name = 'Daemon',                        brand = 'WMC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    daemon2 = { model = 'daemon2',       name = 'Daemon Custom',                 brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    defiler = { model = 'defiler',       name = 'Defiler',                       brand = 'Shitzu',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    deathbike = { model = 'deathbike',     name = 'Deathbike Apocalypse',          brand = 'Deathbike',         category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    deathbike2 = { model = 'deathbike2',    name = 'Deathbike Future Shock',        brand = 'Deathbike',         category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    deathbike3 = { model = 'deathbike3',    name = 'Deathbike Nightmare',           brand = 'Deathbike',         category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    diablous = { model = 'diablous',      name = 'Diablous',                      brand = 'Principe',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    diablous2 = { model = 'diablous2',     name = 'Diablous Custom',               brand = 'Principe',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    double = { model = 'double',        name = 'Double-T',                      brand = 'Dinka',             category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    enduro = { model = 'enduro',        name = 'Enduro',                        brand = 'Dinka',              category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    esskey = { model = 'esskey',        name = 'Esskey',                        brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    faggio = { model = 'faggio',        name = 'Faggio Sport',                  brand = 'Pegassi',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    faggio2 = { model = 'faggio2',       name = 'Faggio',                        brand = 'Pegassi',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    faggio3 = { model = 'faggio3',       name = 'Faggio Mod',                    brand = 'Pegassi',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    fcr = { model = 'fcr',           name = 'FCR 1000',                      brand = 'Pegassi',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    fcr2 = { model = 'fcr2',          name = 'FCR 1000 Custom',               brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    gargoyle = { model = 'gargoyle',      name = 'Gargoyle',                      brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    hakuchou = { model = 'hakuchou',      name = 'Hakuchou',                      brand = 'Shitzu',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    hakuchou2 = { model = 'hakuchou2',     name = 'Hakuchou Drag',                 brand = 'Shitzu',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    hexer = { model = 'hexer',         name = 'Hexer',                         brand = 'LCC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    innovation = { model = 'innovation',    name = 'Innovation',                    brand = 'LLC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    lectro = { model = 'lectro',        name = 'Lectro',                        brand = 'Principe',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    manchez = { model = 'manchez',       name = 'Manchez',                       brand = 'Maibatsu',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    nemesis = { model = 'nemesis',       name = 'Nemesis',                       brand = 'Principe',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    nightblade = { model = 'nightblade',    name = 'Nightblade',                    brand = 'WMC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    oppressor = { model = 'oppressor',     name = 'Oppressor',                     brand = 'Pegassi',         category = 'motorcycles',    type = 'bike',       shop = 'luxury' },
    pcj = { model = 'pcj',           name = 'PCJ-600',                       brand = 'Shitzu',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    ratbike = { model = 'ratbike',       name = 'Rat Bike',                      brand = 'Western',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    ruffian = { model = 'ruffian',       name = 'Ruffian',                       brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    sanchez = { model = 'sanchez',       name = 'Sanchez Livery',                brand = 'Maibatsu',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    sanchez2 = { model = 'sanchez2',      name = 'Sanchez',                       brand = 'Maibatsu',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    sanctus = { model = 'sanctus',       name = 'Sanctus',                       brand = 'LCC',               category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    shotaro = { model = 'shotaro',       name = 'Shotaro',                       brand = 'Nagasaki',         category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    sovereign = { model = 'sovereign',     name = 'Sovereign',                     brand = 'WMC',                category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    stryder = { model = 'stryder',       name = 'Stryder',                       brand = 'Nagasaki',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    thrust = { model = 'thrust',        name = 'Thrust',                        brand = 'Dinka',             category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    vader = { model = 'vader',         name = 'Vader',                         brand = 'Shitzu',             category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    vindicator = { model = 'vindicator',    name = 'Vindicator',                    brand = 'Dinka',             category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    vortex = { model = 'vortex',        name = 'Vortex',                        brand = 'Pegassi',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    wolfsbane = { model = 'wolfsbane',     name = 'Wolfsbane',                     brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    zombiea = { model = 'zombiea',       name = 'Zombie Bobber',                 brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    zombieb = { model = 'zombieb',       name = 'Zombie Chopper',                brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    manchez2 = { model = 'manchez2',      name = 'Manchez Scout',                 brand = 'Maibatsu',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    shinobi = { model = 'shinobi',       name = 'Shinobi',                       brand = 'Nagasaki',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    reever = { model = 'reever',        name = 'Reever',                        brand = 'Western',           category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    manchez3 = { model = 'manchez3',      name = 'Manchez Scout Classic',         brand = 'Maibatsu',          category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    powersurge = { model = 'powersurge',    name = 'Powersurge',                    brand = 'Western',            category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    --- Off-Road (9)
    bfinjection = { model = 'bfinjection',   name = 'Bf Injection',                  brand = 'Annis',              category = 'offroad',        type = 'automobile' },
    bifta = { model = 'bifta',         name = 'Bifta',                         brand = 'Annis',             category = 'offroad',        type = 'automobile' },
    blazer = { model = 'blazer',        name = 'Blazer',                        brand = 'Annis',              category = 'offroad',        type = 'automobile' },
    blazer2 = { model = 'blazer2',       name = 'Blazer Lifeguard',              brand = 'Nagasaki',           category = 'offroad',        type = 'automobile' },
    blazer3 = { model = 'blazer3',       name = 'Blazer Hot Rod',                brand = 'Nagasaki',           category = 'offroad',        type = 'automobile' },
    blazer4 = { model = 'blazer4',       name = 'Blazer Sport',                  brand = 'Annis',              category = 'offroad',        type = 'automobile' },
    blazer5 = { model = 'blazer5',       name = 'Blazer Aqua',                   brand = 'Nagasaki',          category = 'offroad',        type = 'automobile' },
    brawler = { model = 'brawler',       name = 'Brawler',                       brand = 'Annis',             category = 'offroad',        type = 'automobile' },
    caracara = { model = 'caracara',      name = 'Caracara',                      brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    caracara2 = { model = 'caracara2',     name = 'Caracara 4x4',                  brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    dubsta3 = { model = 'dubsta3',       name = 'Dubsta 6x6',                    brand = 'Annis',             category = 'offroad',        type = 'automobile' },
    dune = { model = 'dune',          name = 'Dune Buggy',                    brand = 'Annis',             category = 'offroad',        type = 'automobile' },
    everon = { model = 'everon',        name = 'Everon',                        brand = 'Karin',             category = 'offroad',        type = 'automobile' },
    freecrawler = { model = 'freecrawler',   name = 'Freecrawler',                   brand = 'Canis',             category = 'offroad',        type = 'automobile' },
    hellion = { model = 'hellion',       name = 'Hellion',                       brand = 'Annis',             category = 'offroad',        type = 'automobile' },
    kalahari = { model = 'kalahari',      name = 'Kalahari',                      brand = 'Canis',             category = 'offroad',        type = 'automobile' },
    kamacho = { model = 'kamacho',       name = 'Kamacho',                       brand = 'Canis',             category = 'offroad',        type = 'automobile' },
    mesa3 = { model = 'mesa3',         name = 'Mesa Merryweather',             brand = 'Canis',            category = 'offroad',        type = 'automobile' },
    outlaw = { model = 'outlaw',        name = 'Outlaw',                        brand = 'Nagasaki',          category = 'offroad',        type = 'automobile' },
    rancherxl = { model = 'rancherxl',     name = 'Rancher XL',                    brand = 'Declasse',          category = 'offroad',        type = 'automobile' },
    rebel2 = { model = 'rebel2',        name = 'Rebel',                         brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    riata = { model = 'riata',         name = 'Riata',                         brand = 'Vapid',            category = 'offroad',        type = 'automobile' },
    sandking = { model = 'sandking',      name = 'Sandking XL',                   brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    sandking2 = { model = 'sandking2',     name = 'Sandking SWB',                  brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    trophytruck = { model = 'trophytruck',   name = 'Trophy Truck',                  brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    trophytruck2 = { model = 'trophytruck2',  name = 'Desert Raid',                   brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    vagrant = { model = 'vagrant',       name = 'Vagrant',                       brand = 'Maxwell',           category = 'offroad',        type = 'automobile' },
    verus = { model = 'verus',         name = 'Verus',                         brand = 'Dinka',             category = 'offroad',        type = 'automobile' },
    winky = { model = 'winky',         name = 'Winky',                         brand = 'Vapid',             category = 'offroad',        type = 'automobile' },
    yosemite3 = { model = 'yosemite3',     name = 'Yosemite Rancher',              brand = 'Declasse',         category = 'offroad',        type = 'automobile' },
    mesa = { model = 'mesa',          name = 'Mesa',                          brand = 'Canis',             category = 'offroad',        type = 'automobile' },
    ratel = { model = 'ratel',         name = 'Ratel',                         brand = 'Vapid',            category = 'offroad',        type = 'automobile' },
    l35 = { model = 'l35',           name = 'Walton L35',                    brand = 'Declasse',         category = 'offroad',        type = 'automobile' },
    monstrociti = { model = 'monstrociti',   name = 'MonstroCiti',                   brand = 'Maibatsu',          category = 'offroad',        type = 'automobile' },
    draugur = { model = 'draugur',       name = 'Draugur',                       brand = 'Declasse',          category = 'offroad',        type = 'automobile' },
    terminus = { model = 'terminus',      name = 'Terminus',                      brand = 'Canis',             category = 'offroad',       type = 'automobile' },
    --- Industrial (10)
    guardian = { model = 'guardian',      name = 'Guardian',                      brand = 'Vapid',             category = 'industrial',     type = 'automobile' },
    mixer2 = { model = 'mixer2',        name = 'Mixer II',                      brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    tiptruck2 = { model = 'tiptruck2',     name = 'Tipper II',                     brand = 'Brute',             category = 'industrial',     type = 'automobile' },
    tiptruck = { model = 'tiptruck',      name = 'Tipper',                        brand = 'Brute',             category = 'industrial',     type = 'automobile' },
    rubble = { model = 'rubble',        name = 'Rubble',                        brand = 'Jobuilt',           category = 'industrial',     type = 'automobile' },
    mixer = { model = 'mixer',         name = 'Mixer',                         brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    flatbed = { model = 'flatbed',       name = 'Flatbed Truck',                 brand = 'MTL',               category = 'industrial',     type = 'automobile' },
    dump = { model = 'dump',          name = 'Dump Truck',                    brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    bulldozer = { model = 'bulldozer',     name = 'Dozer',                         brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    handler = { model = 'handler',       name = 'Dock Handler',                  brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    cutter = { model = 'cutter',        name = 'Cutter',                        brand = 'HVY',               category = 'industrial',     type = 'automobile' },
    --- Utility (11)
    slamtruck = { model = 'slamtruck',     name = 'Slam Truck',                    brand = 'Vapid',            category = 'utility',        type = 'automobile' },
    caddy3 = { model = 'caddy3',        name = 'Caddy (Bunker)',                brand = 'Nagasaki',          category = 'utility',        type = 'automobile' },
    caddy2 = { model = 'caddy2',        name = 'Caddy (Civilian)',              brand = 'Nagasaki',          category = 'utility',        type = 'automobile' },
    caddy3 = { model = 'caddy3',        name = 'Caddy (Golf)',                  brand = 'Nagasaki',          category = 'utility',        type = 'automobile' },
    utillitruck = { model = 'utillitruck',   name = 'Utility Truck (Cherry Picker)', brand = 'Brute',             category = 'utility',        type = 'automobile' },
    utillitruck2 = { model = 'utillitruck2',  name = 'Utility Truck (Van)',           brand = 'Brute',             category = 'utility',        type = 'automobile' },
    utillitruck3 = { model = 'utillitruck3',  name = 'Utility Truck (Contender)',     brand = 'Vapid',             category = 'utility',        type = 'automobile' },
    tractor = { model = 'tractor',       name = 'Tractor',                       brand = 'Stanley',           category = 'utility',        type = 'automobile' },
    tractor2 = { model = 'tractor2',      name = 'Fieldmaster',                   brand = 'Stanley',           category = 'utility',        type = 'automobile' },
    tractor3 = { model = 'tractor3',      name = 'Fieldmaster',                   brand = 'Stanley',           category = 'utility',        type = 'automobile' },
    towtruck = { model = 'towtruck',      name = 'Tow Truck (Large)',             brand = 'Vapid',             category = 'utility',        type = 'automobile' },
    towtruck2 = { model = 'towtruck2',     name = 'Tow Truck (Small)',             brand = 'Vapid',             category = 'utility',        type = 'automobile' },
    scrap = { model = 'scrap',         name = 'Scrap Truck',                   brand = 'Vapid',             category = 'utility',        type = 'automobile' },
    sadler = { model = 'sadler',        name = 'Sadler',                        brand = 'Vapid',             category = 'utility',        type = 'automobile' },
    ripley = { model = 'ripley',        name = 'Ripley',                        brand = 'HVY',               category = 'utility',        type = 'automobile' },
    mower = { model = 'mower',         name = 'Lawn Mower',                    brand = 'Jacksheepe',        category = 'utility',        type = 'automobile' },
    forklift = { model = 'forklift',      name = 'Forklift',                      brand = 'HVY',               category = 'utility',        type = 'automobile' },
    docktug = { model = 'docktug',       name = 'Docktug',                       brand = 'HVY',               category = 'utility',        type = 'automobile' },
    airtug = { model = 'airtug',        name = 'Airtug',                        brand = 'HVY',               category = 'utility',        type = 'automobile' },
    trailers5 = { model = 'trailers5',     name = 'Trailer (Christmas)',           brand = 'Unknown',           category = 'utility',        type = 'automobile' },
    tvtrailer2 = { model = 'tvtrailer2',    name = 'Trailer (TV)',                  brand = 'Unknown',           category = 'utility',        type = 'automobile' },
    --- Vans (12)
    bison = { model = 'bison',         name = 'Bison',                         brand = 'Bravado',           category = 'vans',           type = 'automobile' },
    bobcatxl = { model = 'bobcatxl',      name = 'Bobcat XL Open',                brand = 'Vapid',             category = 'vans',           type = 'automobile' },
    burrito3 = { model = 'burrito3',      name = 'Burrito',                       brand = 'Declasse',           category = 'vans',           type = 'automobile' },
    gburrito2 = { model = 'gburrito2',     name = 'Burrito Custom',                brand = 'Declasse',          category = 'vans',           type = 'automobile' },
    rumpo = { model = 'rumpo',         name = 'Rumpo',                         brand = 'Bravado',            category = 'vans',           type = 'automobile' },
    journey = { model = 'journey',       name = 'Journey',                       brand = 'Zirconium',          category = 'vans',           type = 'automobile' },
    minivan = { model = 'minivan',       name = 'Minivan',                       brand = 'Vapid',              category = 'vans',           type = 'automobile' },
    minivan2 = { model = 'minivan2',      name = 'Minivan Custom',                brand = 'Vapid',             category = 'vans',           type = 'automobile' },
    paradise = { model = 'paradise',      name = 'Paradise',                      brand = 'Bravado',            category = 'vans',           type = 'automobile' },
    rumpo3 = { model = 'rumpo3',        name = 'Rumpo Custom',                  brand = 'Bravado',           category = 'vans',           type = 'automobile' },
    speedo = { model = 'speedo',        name = 'Speedo',                        brand = 'Vapid',             category = 'vans',           type = 'automobile' },
    speedo4 = { model = 'speedo4',       name = 'Speedo Custom',                 brand = 'Vapid',             category = 'vans',           type = 'automobile' },
    surfer = { model = 'surfer',        name = 'Surfer',                        brand = 'BF',                 category = 'vans',           type = 'automobile' },
    youga3 = { model = 'youga3',        name = 'Youga Classic 4x4',             brand = 'Bravado',           category = 'vans',           type = 'automobile' },
    youga = { model = 'youga',         name = 'Youga',                         brand = 'Bravado',            category = 'vans',           type = 'automobile' },
    youga2 = { model = 'youga2',        name = 'Youga Classic',                 brand = 'Bravado',           category = 'vans',           type = 'automobile' },
    youga4 = { model = 'youga4',        name = 'Youga Custom',                  brand = 'Bravado',           category = 'vans',           type = 'automobile' },
    moonbeam = { model = 'moonbeam',      name = 'Moonbeam',                      brand = 'Declasse',          category = 'vans',           type = 'automobile' },
    moonbeam2 = { model = 'moonbeam2',     name = 'Moonbeam Custom',               brand = 'Declasse',          category = 'vans',           type = 'automobile' },
    boxville = { model = 'boxville',      name = 'Boxville LSDWP',                brand = 'Brute',             category = 'vans',           type = 'automobile' },
    boxville2 = { model = 'boxville2',     name = 'Boxville Go Postal',            brand = 'Brute',             category = 'vans',           type = 'automobile' },
    boxville3 = { model = 'boxville3',     name = 'Boxville Humane Labs',          brand = 'Brute',             category = 'vans',           type = 'automobile' },
    boxville4 = { model = 'boxville4',     name = 'Boxville Post OP',              brand = 'Brute',             category = 'vans',           type = 'automobile' },
    boxville5 = { model = 'boxville5',     name = 'Armored Boxville',              brand = 'Brute',             category = 'vans',           type = 'automobile' },
    pony = { model = 'pony',          name = 'Pony',                          brand = 'Brute',             category = 'vans',           type = 'automobile' },
    pony2 = { model = 'pony2',         name = 'Pony (Smoke on the water)',     brand = 'Brute',             category = 'vans',           type = 'automobile' },
    journey2 = { model = 'journey2',      name = 'Journey II',                    brand = 'Zirconium',          category = 'vans',           type = 'automobile' },
    surfer3 = { model = 'surfer3',       name = 'Surfer Custom',                 brand = 'BF',                category = 'vans',           type = 'automobile' },
    speedo5 = { model = 'speedo5',       name = 'Speedo Custom',                 brand = 'Vapid',            category = 'vans',           type = 'automobile' },
    mule2 = { model = 'mule2',         name = 'Mule',                          brand = 'Maibatsu',          category = 'vans',           type = 'automobile' },
    mule3 = { model = 'mule3',         name = 'Mule',                          brand = 'Maibatsu',          category = 'vans',           type = 'automobile' },
    taco = { model = 'taco',          name = 'Taco Truck',                    brand = 'Brute',             category = 'vans',           type = 'automobile' },
    boxville6 = { model = 'boxville6',     name = 'Boxville (LSDS)',               brand = 'Brute',             category = 'vans',           type = 'automobile' },
    --- Cycles (13)
    bmx = { model = 'bmx',           name = 'BMX',                           brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    cruiser = { model = 'cruiser',       name = 'Cruiser',                       brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    fixter = { model = 'fixter',        name = 'Fixter',                        brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    scorcher = { model = 'scorcher',      name = 'Scorcher',                      brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    tribike = { model = 'tribike',       name = 'Whippet Race Bike',             brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    tribike2 = { model = 'tribike2',      name = 'Endurex Race Bike',             brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    tribike3 = { model = 'tribike3',      name = 'Tri-Cycles Race Bike',          brand = 'Bike',                category = 'cycles',         type = 'bike',       shop = 'pdm' },
    inductor = { model = 'inductor',      name = 'Inductor',                      brand = 'Coil',               category = 'cycles',         type = 'bike',       shop = 'pdm' },
    inductor2 = { model = 'inductor2',     name = 'Junk Energy Inductor',          brand = 'Coil',               category = 'cycles',         type = 'bike',       shop = 'pdm' },
    --- Boats (14)
    avisa = { model = 'avisa',         name = 'Avisa',                         brand = 'Kraken Subs',       category = 'boats',          type = 'boat',       shop = 'none' },
    patrolboat = { model = 'patrolboat',    name = 'Kurtz 31 Patrol Boat',          brand = 'Unknown',           category = 'boats',          type = 'boat',       shop = 'none' },
    longfin = { model = 'longfin',       name = 'Longfin',                       brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    tug = { model = 'tug',           name = 'Tug',                           brand = 'Buckingham',        category = 'boats',          type = 'boat',       shop = 'none' },
    toro = { model = 'toro',          name = 'Toro',                          brand = 'Lampadati',         category = 'boats',          type = 'boat',       shop = 'boats' },
    toro2 = { model = 'toro2',         name = 'Toro Yacht',                    brand = 'Lampadati',         category = 'boats',          type = 'boat',       shop = 'boats' },
    submersible2 = { model = 'submersible2',  name = 'Kraken',                        brand = 'Kraken Subs',       category = 'boats',          type = 'boat',       shop = 'none' },
    speeder = { model = 'speeder',       name = 'Speeder',                       brand = 'Pegassi',           category = 'boats',          type = 'boat',       shop = 'boats' },
    speeder2 = { model = 'speeder2',      name = 'Speeder Yacht',                 brand = 'Pegassi',           category = 'boats',          type = 'boat',       shop = 'boats' },
    tropic = { model = 'tropic',        name = 'Tropic',                        brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    tropic2 = { model = 'tropic2',       name = 'Tropic Yacht',                  brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    suntrap = { model = 'suntrap',       name = 'Suntrap',                       brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    submersible = { model = 'submersible',   name = 'Submersible',                   brand = 'Kraken Subs',       category = 'boats',          type = 'boat',       shop = 'none' },
    squalo = { model = 'squalo',        name = 'Squalo',                        brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    seashark = { model = 'seashark',      name = 'Seashark',                      brand = 'Speedophile',       category = 'boats',          type = 'boat',       shop = 'boats' },
    seashark3 = { model = 'seashark3',     name = 'Seashark Yacht',                brand = 'Speedophile',       category = 'boats',          type = 'boat',       shop = 'boats' },
    marquis = { model = 'marquis',       name = 'Marquis',                       brand = 'Dinka',             category = 'boats',          type = 'boat',       shop = 'boats' },
    jetmax = { model = 'jetmax',        name = 'Jetmax',                        brand = 'Shitzu',            category = 'boats',          type = 'boat',       shop = 'boats' },
    dinghy = { model = 'dinghy',        name = 'Dinghy 2-Seater',               brand = 'Nagasaki',          category = 'boats',          type = 'boat',       shop = 'boats' },
    dinghy2 = { model = 'dinghy2',       name = 'Dinghy 4-Seater',               brand = 'Nagasaki',          category = 'boats',          type = 'boat',       shop = 'boats' },
    dinghy3 = { model = 'dinghy3',       name = 'Dinghy (Heist)',                brand = 'Nagasaki',          category = 'boats',          type = 'boat',       shop = 'boats' },
    dinghy4 = { model = 'dinghy4',       name = 'Dinghy Yacht',                  brand = 'Nagasaki',          category = 'boats',          type = 'boat',       shop = 'boats' },
    --- Helicopters (15)
    conada2 = { model = 'conada2',       name = 'Weaponized Conada',             brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'none' },
    conada = { model = 'conada',        name = 'Conada',                        brand = 'Buckingham',       category = 'helicopters',    type = 'heli',       shop = 'air' },
    seasparrow2 = { model = 'seasparrow2',   name = 'Sparrow',                       brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'air' },
    annihilator2 = { model = 'annihilator2',  name = 'Annihilator Stealth',           brand = 'Western Company',   category = 'helicopters',    type = 'heli',       shop = 'none' },
    seasparrow = { model = 'seasparrow',    name = 'Sea Sparrow',                   brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'air' },
    akula = { model = 'akula',         name = 'Akula',                         brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'none' },
    hunter = { model = 'hunter',        name = 'FH-1 Hunter',                   brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'none' },
    havok = { model = 'havok',         name = 'Havok',                         brand = 'Nagasaki',          category = 'helicopters',    type = 'heli',       shop = 'air' },
    volatus = { model = 'volatus',       name = 'Volatus',                       brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    supervolito2 = { model = 'supervolito2',  name = 'SuperVolito Carbon',            brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    supervolito = { model = 'supervolito',   name = 'SuperVolito',                   brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    swift2 = { model = 'swift2',        name = 'Swift Deluxe',                  brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    valkyrie = { model = 'valkyrie',      name = 'Valkyrie',                      brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'none' },
    savage = { model = 'savage',        name = 'Savage',                        brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'none' },
    swift = { model = 'swift',         name = 'Swift',                         brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    annihilator = { model = 'annihilator',   name = 'Annihilator',                   brand = 'Western Company',   category = 'helicopters',    type = 'heli',       shop = 'none' },
    cargobob2 = { model = 'cargobob2',     name = 'Cargobob Jetsam',               brand = 'Western Company',   category = 'helicopters',    type = 'heli',       shop = 'none' },
    skylift = { model = 'skylift',       name = 'Skylift',                       brand = 'HVY',               category = 'helicopters',    type = 'heli',       shop = 'none' },
    maverick = { model = 'maverick',      name = 'Maverick',                      brand = 'Buckingham',        category = 'helicopters',    type = 'heli',       shop = 'air' },
    frogger = { model = 'frogger',       name = 'Frogger',                       brand = 'Maibatsu',          category = 'helicopters',    type = 'heli',       shop = 'air' },
    frogger2 = { model = 'frogger2',      name = 'Frogger',                       brand = 'Maibatsu',          category = 'helicopters',    type = 'heli',       shop = 'air' },
    cargobob = { model = 'cargobob',      name = 'Cargobob',                      brand = 'Western Company',   category = 'helicopters',    type = 'heli',       shop = 'none' },
    cargobob3 = { model = 'cargobob3',     name = 'Cargobob',                      brand = 'Western Company',   category = 'helicopters',    type = 'heli',       shop = 'none' },
    seasparrow3 = { model = 'seasparrow3',   name = 'Sparrow (Prop)',                brand = 'Unknown',           category = 'helicopters',    type = 'heli',       shop = 'none' },
    buzzard = { model = 'buzzard',       name = 'Buzzard Attack Chopper',        brand = 'Nagasaki',          category = 'helicopters',    type = 'heli',       shop = 'none' },
    buzzard2 = { model = 'buzzard2',      name = 'Buzzard',                       brand = 'Nagasaki',          category = 'helicopters',    type = 'heli',       shop = 'none' },
    --- Planes (16)
    streamer216 = { model = 'streamer216',   name = 'Streamer216',                   brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    raiju = { model = 'raiju',         name = 'F-160 Raiju',                   brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    alkonost = { model = 'alkonost',      name = 'RO-86 Alkonost',                brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    strikeforce = { model = 'strikeforce',   name = 'B-11 Strikeforce',              brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    blimp3 = { model = 'blimp3',        name = 'Blimp',                         brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    avenger = { model = 'avenger',       name = 'Avenger',                       brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    avenger2 = { model = 'avenger2',      name = 'Avenger',                       brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    volatol = { model = 'volatol',       name = 'Volatol',                       brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    nokota = { model = 'nokota',        name = 'P-45 Nokota',                   brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    seabreeze = { model = 'seabreeze',     name = 'Seabreeze',                     brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'air' },
    pyro = { model = 'pyro',          name = 'Pyro',                          brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'none' },
    mogul = { model = 'mogul',         name = 'Mogul',                         brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    howard = { model = 'howard',        name = 'Howard NX-25',                  brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    bombushka = { model = 'bombushka',     name = 'RM-10 Bombushka',               brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    molotok = { model = 'molotok',       name = 'V-65 Molotok',                  brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    microlight = { model = 'microlight',    name = 'Ultralight',                    brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'air' },
    tula = { model = 'tula',          name = 'Tula',                          brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    rogue = { model = 'rogue',         name = 'Rogue',                         brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'none' },
    starling = { model = 'starling',      name = 'LF-22 Starling',                brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    alphaz1 = { model = 'alphaz1',       name = 'Alpha-Z1',                      brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'none' },
    nimbus = { model = 'nimbus',        name = 'Nimbus',                        brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    luxor2 = { model = 'luxor2',        name = 'Luxor Deluxe',                  brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    velum2 = { model = 'velum2',        name = 'Velum 5-seater',                brand = 'JoBuilt',           category = 'planes',         type = 'plane',      shop = 'air' },
    hydra = { model = 'hydra',         name = 'Hydra',                         brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'none' },
    blimp2 = { model = 'blimp2',        name = 'Xero Blimp',                    brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    dodo = { model = 'dodo',          name = 'Dodo',                          brand = 'Mammoth',           category = 'planes',         type = 'plane',      shop = 'air' },
    miljet = { model = 'miljet',        name = 'Miljet',                        brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    besra = { model = 'besra',         name = 'Besra',                         brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'none' },
    vestra = { model = 'vestra',        name = 'Vestra',                        brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    cargoplane = { model = 'cargoplane',    name = 'Cargo Plane',                   brand = 'JoBuilt',           category = 'planes',         type = 'plane',      shop = 'air' },
    velum = { model = 'velum',         name = 'Velum',                         brand = 'JoBuilt',           category = 'planes',         type = 'plane',      shop = 'air' },
    titan = { model = 'titan',         name = 'Titan',                         brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    shamal = { model = 'shamal',        name = 'Shamal',                        brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    lazer = { model = 'lazer',         name = 'P-996 Lazer',                   brand = 'JoBuilt',           category = 'planes',         type = 'plane',      shop = 'none' },
    mammatus = { model = 'mammatus',      name = 'Mammatus',                      brand = 'JoBuilt',           category = 'planes',         type = 'plane',      shop = 'air' },
    stunt = { model = 'stunt',         name = 'Mallard',                       brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'air' },
    luxor = { model = 'luxor',         name = 'Luxor',                         brand = 'Buckingham',        category = 'planes',         type = 'plane',      shop = 'air' },
    jet = { model = 'jet',           name = 'Jet',                           brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    duster = { model = 'duster',        name = 'Duster',                        brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'air' },
    cuban800 = { model = 'cuban800',      name = 'Cuban 800',                     brand = 'Western Company',   category = 'planes',         type = 'plane',      shop = 'air' },
    blimp = { model = 'blimp',         name = 'Atomic Blimp',                  brand = 'Unknown',           category = 'planes',         type = 'plane',      shop = 'none' },
    --- Service (17)
    brickade = { model = 'brickade',      name = 'Brickade',                      brand = 'MTL',              category = 'service',        type = 'automobile' },
    brickade2 = { model = 'brickade2',     name = 'Brickade 6x6',                  brand = 'MTL',              category = 'service',        type = 'automobile' },
    pbus2 = { model = 'pbus2',         name = 'Festival Bus',                  brand = 'Unknown',          category = 'service',        type = 'automobile' },
    wastelander = { model = 'wastelander',   name = 'Wastelander',                   brand = 'MTL',              category = 'service',        type = 'automobile' },
    rallytruck = { model = 'rallytruck',    name = 'Dune',                          brand = 'MTL',              category = 'service',        type = 'automobile' },
    metrotrain = { model = 'metrotrain',    name = 'Metro Train',                   brand = 'Unknown',          category = 'service',        type = 'automobile' },
    freight = { model = 'freight',       name = 'Freight Train',                 brand = 'Unknown',          category = 'service',        type = 'automobile' },
    cablecar = { model = 'cablecar',      name = 'Cable Car',                     brand = 'Unknown',          category = 'service',        type = 'automobile' },
    trash = { model = 'trash',         name = 'Trashmaster',                   brand = 'JoBuilt',          category = 'service',        type = 'automobile' },
    trash2 = { model = 'trash2',        name = 'Trashmaster',                   brand = 'JoBuilt',          category = 'service',        type = 'automobile' },
    tourbus = { model = 'tourbus',       name = 'Tour Bus',                      brand = 'Brute',            category = 'service',        type = 'automobile' },
    taxi = { model = 'taxi',          name = 'Taxi',                          brand = 'Vapid',            category = 'service',        type = 'automobile' },
    rentalbus = { model = 'rentalbus',     name = 'Rental Shuttle Bus',            brand = 'Brute',            category = 'service',        type = 'automobile' },
    coach = { model = 'coach',         name = 'Dashound',                      brand = 'Brute',            category = 'service',        type = 'automobile' },
    bus = { model = 'bus',           name = 'Bus',                           brand = 'Brute',            category = 'service',        type = 'automobile' },
    airbus = { model = 'airbus',        name = 'Airport Bus',                   brand = 'Brute',            category = 'service',        type = 'automobile' },
    --- Emergency (18)
    riot = { model = 'riot',          name = 'Police Riot',                   brand = 'Brute',            category = 'emergency',      type = 'automobile' },
    riot2 = { model = 'riot2',         name = 'RCV',                           brand = 'Unknown',          category = 'emergency',      type = 'automobile' },
    pbus = { model = 'pbus',          name = 'Police Prison Bus',             brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    police = { model = 'police',        name = 'Police Cruiser',                brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    police2 = { model = 'police2',       name = 'Police Buffalo',                brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    police3 = { model = 'police3',       name = 'Police Interceptor',            brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    police4 = { model = 'police4',       name = 'Unmarked Cruiser',              brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    sheriff = { model = 'sheriff',       name = 'Sheriff SUV',                   brand = 'Declasse',         category = 'emergency',      type = 'automobile' },
    sheriff2 = { model = 'sheriff2',      name = 'Sheriff Cruiser',               brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    policeold1 = { model = 'policeold1',    name = 'Police Rancher',                brand = 'Declasse',         category = 'emergency',      type = 'automobile' },
    policeold2 = { model = 'policeold2',    name = 'Police Roadcruiser',            brand = 'Albany',           category = 'emergency',      type = 'automobile' },
    policet = { model = 'policet',       name = 'Police Transporter',            brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    policeb = { model = 'policeb',       name = 'Police Bike',                   brand = 'Vapid',            category = 'emergency',      type = 'automobile' },
    polmav = { model = 'polmav',        name = 'Police Maverick',               brand = 'Buckingham',       category = 'emergency',      type = 'automobile' },
    ambulance = { model = 'ambulance',     name = 'Ambulance',                     brand = 'Brute',            category = 'emergency',      type = 'automobile' },
    firetruk = { model = 'firetruk',      name = 'Fire Truck',                    brand = 'MTL',              category = 'emergency',      type = 'automobile' },
    lguard = { model = 'lguard',        name = 'Lifeguard',                     brand = 'Declasse',         category = 'emergency',      type = 'automobile' },
    seashark2 = { model = 'seashark2',     name = 'Seashark Lifeguard',            brand = 'Speedophile',       category = 'emergency',      type = 'automobile' },
    pranger = { model = 'pranger',       name = 'Park Ranger',                   brand = 'Declasse',          category = 'emergency',      type = 'automobile' },
    fbi = { model = 'fbi',           name = 'FIB Buffalo',                   brand = 'Bravado',           category = 'emergency',      type = 'automobile' },
    fbi2 = { model = 'fbi2',          name = 'FIB Granger',                   brand = 'Declasse',          category = 'emergency',      type = 'automobile' },
    predator = { model = 'predator',      name = 'Police Predator',               brand = 'Unknown',           category = 'emergency',      type = 'automobile' },
    polgauntlet = { model = 'polgauntlet',   name = 'Gauntlet Interceptor',          brand = 'Bravado',           category = 'emergency',      type = 'automobile' },
    police5 = { model = 'police5',       name = 'Stanier LE Cruiser',            brand = 'Vapid',             category = 'emergency',      type = 'automobile' },
    --- Military (19)
    vetir = { model = 'vetir',         name = 'Vetir',                         brand = 'Unknown',          category = 'military',       type = 'automobile' },
    kosatka = { model = 'kosatka',       name = 'Kosatka',                       brand = 'Rune',             category = 'military',       type = 'automobile' },
    minitank = { model = 'minitank',      name = 'RC Tank',                       brand = 'Unknown',          category = 'military',       type = 'automobile' },
    scarab = { model = 'scarab',        name = 'Scarab',                        brand = 'HVY',              category = 'military',       type = 'automobile' },
    terbyte = { model = 'terbyte',       name = 'Terrorbyte',                    brand = 'Benefactor',       category = 'military',       type = 'automobile' },
    thruster = { model = 'thruster',      name = 'Thruster',                      brand = 'Mammoth',          category = 'military',       type = 'automobile' },
    khanjali = { model = 'khanjali',      name = 'TM-02 Khanjali Tank',           brand = 'Unknown',          category = 'military',       type = 'automobile' },
    chernobog = { model = 'chernobog',     name = 'Chernobog',                     brand = 'HVY',              category = 'military',       type = 'automobile' },
    barrage = { model = 'barrage',       name = 'Barrage',                       brand = 'HVY',              category = 'military',       type = 'automobile' },
    trailerlarge = { model = 'trailerlarge',  name = 'Mobile Operations Center',      brand = 'Unknown',          category = 'military',       type = 'automobile' },
    halftrack = { model = 'halftrack',     name = 'Half-track',                    brand = 'Bravado',          category = 'military',       type = 'automobile' },
    apc = { model = 'apc',           name = 'APC Tank',                      brand = 'HVY',              category = 'military',       type = 'automobile' },
    trailersmall2 = { model = 'trailersmall2', name = 'Anti-Aircraft Trailer',         brand = 'Vom Feuer',        category = 'military',       type = 'automobile' },
    rhino = { model = 'rhino',         name = 'Rhino Tank',                    brand = 'Unknown',          category = 'military',       type = 'automobile' },
    crusader = { model = 'crusader',      name = 'Crusader',                      brand = 'Canis',            category = 'military',       type = 'automobile' },
    barracks = { model = 'barracks',      name = 'Barracks',                      brand = 'HVY',              category = 'military',       type = 'automobile' },
    barracks2 = { model = 'barracks2',     name = 'Barracks Semi',                 brand = 'HVY',              category = 'military',       type = 'automobile' },
    barracks3 = { model = 'barracks3',     name = 'Barracks',                      brand = 'HVY',              category = 'military',       type = 'automobile' },
    --- Commercial (20)
    cerberus = { model = 'cerberus',      name = 'Apocalypse Cerberus',           brand = 'MTL',              category = 'commercial',     type = 'automobile' },
    pounder2 = { model = 'pounder2',      name = 'Pounder Custom',                brand = 'MTL',               category = 'commercial',     type = 'automobile' },
    mule4 = { model = 'mule4',         name = 'Mule Custom',                   brand = 'Maibatsu',          category = 'commercial',     type = 'automobile' },
    phantom3 = { model = 'phantom3',      name = 'Phantom Custom',                brand = 'Jobuilt',          category = 'commercial',     type = 'automobile' },
    hauler2 = { model = 'hauler2',       name = 'Hauler Custom',                 brand = 'Jobuilt',          category = 'commercial',     type = 'automobile' },
    phantom2 = { model = 'phantom2',      name = 'Phantom Wedge',                 brand = 'Jobuilt',          category = 'commercial',     type = 'automobile' },
    mule5 = { model = 'mule5',         name = 'Mule (Heist)',                  brand = 'Maibatsu',          category = 'commercial',     type = 'automobile' },
    stockade = { model = 'stockade',      name = 'Stockade',                      brand = 'Brute',            category = 'commercial',     type = 'automobile' },
    pounder = { model = 'pounder',       name = 'Pounder',                       brand = 'MTL',               category = 'commercial',     type = 'automobile' },
    phantom = { model = 'phantom',       name = 'Phantom',                       brand = 'Jobuilt',          category = 'commercial',     type = 'automobile' },
    packer = { model = 'packer',        name = 'Packer',                        brand = 'MTL',              category = 'commercial',     type = 'automobile' },
    mule = { model = 'mule',          name = 'Mule',                          brand = 'Maibatsu',          category = 'commercial',     type = 'automobile' },
    hauler = { model = 'hauler',        name = 'Hauler',                        brand = 'Jobuilt',          category = 'commercial',     type = 'automobile' },
    biff = { model = 'biff',          name = 'Biff',                          brand = 'Brute',            category = 'commercial',     type = 'automobile' },
    benson = { model = 'benson',        name = 'Benson',                        brand = 'Vapid',             category = 'commercial',     type = 'automobile' },
    benson2 = { model = 'benson2',       name = 'Benson (Cluckin Bell)',         brand = 'Vapid',             category = 'commercial',     type = 'automobile' },
    phantom4 = { model = 'phantom4',      name = 'Phantom (Christmas)',           brand = 'Vapid',            category = 'commercial',     type = 'automobile' },
    --- Trains (21)
    --- Open Wheel (22)
    openwheel2 = { model = 'openwheel2',    name = 'DR1',                           brand = 'Declasse',         category = 'openwheel',      type = 'automobile' },
    openwheel1 = { model = 'openwheel1',    name = 'BR8',                           brand = 'Benefactor',       category = 'openwheel',      type = 'automobile' },
    formula2 = { model = 'formula2',      name = 'R88',                           brand = 'Ocelot',           category = 'openwheel',      type = 'automobile' },
    formula = { model = 'formula',       name = 'PR4',                           brand = 'Progen',           category = 'openwheel',      type = 'automobile' },
    default = { model = 'default',       name = 'Default',                           brand = 'Progen',           category = 'openwheel',      type = 'automobile' },
}