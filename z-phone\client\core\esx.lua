if Config.Core == "ESX" then
    xCore = {}
    local ESX = exports["Framework"]:getSharedObject()
    local inventory = exports.inventory

    xCore.GetPlayerData = function()
        local ply = ESX.GetPlayerData()
        if not ply then return nil end
        return {
            citizenid = ply.identifier
        }
    end

    xCore.Notify = function(msg, typ, time)
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = false,
            args = {"PHONE", msg}
        })
    end

    xCore.HasItemByName = function(item)
        local items = inventory:GetInventoryItems()
        if items then
            for i = 1, #items do
                if items[i].name == item and items[i].quantity > 0 then
                    return true
                end
            end
        end
        return false
    end

    xCore.GetClosestPlayer = function ()
        return ESX.Game.GetClosestPlayer()
    end
end