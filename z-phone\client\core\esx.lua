if Config.Core == "ESX" then
    xCore = {}
    local ESX = exports["Framework"]:getSharedObject()
    local inventory = exports.inventory

    xCore.GetPlayerData = function()
        local ply = ESX.GetPlayerData()
        if not ply then return nil end
        return {
            citizenid = ply.identifier
        }
    end

    xCore.Notify = function(msg, typ, time)
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = false,
            args = {"PHONE", msg}
        })
    end

    xCore.HasItemByName = function(item)
        local quantity = inventory:GetItemQuantityBy({ name = item })
        return quantity and quantity > 0
    end

    xCore.GetClosestPlayer = function ()
        return ESX.Game.GetClosestPlayer()
    end
end